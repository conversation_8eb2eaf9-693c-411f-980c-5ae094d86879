'use client'

import React, { useEffect, useState, useCallback } from 'react'
import { use<PERSON><PERSON>, Controller } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { Button, TextInput, SelectInput, CheckboxInput, Gutter, toast } from '@payloadcms/ui'

// Option type for Payload SelectInput
interface Option {
  label: string
  value: string
}

// Types for API responses
interface User {
  id: number
  email: string
  firstName?: string
  lastName?: string
}

interface Ticket {
  id: number
  ticketCode: string
  seat?: string
  attendeeName?: string
  status: string
  event?: {
    id: number
    title: string
    eventDate?: string
  }
  ticketPriceName?: string
  createdAt: string
}

// Validation schema using zod
const schema = z.object({
  ownerId: z.string().min(1, 'Ticket owner is required'),
  ticketIds: z.array(z.number()).min(1, 'Select at least one ticket'),
  recipientFirstName: z.string().min(1, 'First name is required'),
  recipientLastName: z.string().min(1, 'Last name is required'),
  recipientEmail: z.string().min(1, 'Email is required').email('Invalid email'),
  recipientPhone: z.string().optional(),
  message: z.string().optional(),
})

type FormValues = z.infer<typeof schema>

const CreateTicketGiftForm: React.FC = () => {
  const [users, setUsers] = useState<User[]>([])
  const [tickets, setTickets] = useState<Ticket[]>([])
  const [selectedOwner, setSelectedOwner] = useState<string>('')
  const [loadingUsers, setLoadingUsers] = useState(false)
  const [loadingTickets, setLoadingTickets] = useState(false)
  const [submitting, setSubmitting] = useState(false)
  const [searchTimeout, setSearchTimeout] = useState<NodeJS.Timeout | null>(null)

  const {
    handleSubmit,
    setValue,
    watch,
    control,
    formState: { errors, isSubmitting },
    reset,
  } = useForm<FormValues>({
    resolver: zodResolver(schema),
    defaultValues: {
      ownerId: '',
      ticketIds: [],
      recipientFirstName: '',
      recipientLastName: '',
      recipientEmail: '',
      recipientPhone: '',
      message: '',
    },
  })

  // Fetch users from API using PayloadCMS endpoint
  const fetchUsers = useCallback(async (search = '') => {
    setLoadingUsers(true)
    try {
      let url =
        '/api/users?depth=0&limit=50&select[email]=true&select[firstName]=true&select[lastName]=true&select[id]=true'

      if (search) {
        // Use PayloadCMS where clause for email search
        url += `&where[email][like]=${encodeURIComponent(search)}`
      }

      const response = await fetch(url)
      if (response.ok) {
        const data = await response.json()
        setUsers(data.docs || [])
      } else {
        toast.error('Failed to fetch users')
      }
    } catch (error) {
      console.error('Error fetching users:', error)
      toast.error('Failed to fetch users')
    } finally {
      setLoadingUsers(false)
    }
  }, [])

  // Fetch tickets for selected owner using PayloadCMS API
  const fetchTickets = useCallback(async (userId: string) => {
    if (!userId) {
      setTickets([])
      return
    }

    setLoadingTickets(true)
    try {
      // Use PayloadCMS tickets API with filters
      const url = `/api/tickets?depth=1&limit=100&where[user][equals]=${userId}&where[status][equals]=booked&select[id]=true&select[ticketCode]=true&select[seat]=true&select[attendeeName]=true&select[ticketPriceName]=true&select[event]=true&select[eventDate]=true&select[createdAt]=true`

      const response = await fetch(url)
      if (response.ok) {
        const data = await response.json()
        setTickets(data.docs || [])
      } else {
        toast.error('Failed to fetch tickets')
        setTickets([])
      }
    } catch (error) {
      console.error('Error fetching tickets:', error)
      toast.error('Failed to fetch tickets')
      setTickets([])
    } finally {
      setLoadingTickets(false)
    }
  }, [])

  // Debounced search function
  const debouncedSearch = useCallback(
    (searchTerm: string) => {
      if (searchTimeout) {
        clearTimeout(searchTimeout)
      }

      const timeout = setTimeout(() => {
        fetchUsers(searchTerm)
      }, 300) // 300ms delay

      setSearchTimeout(timeout)
    },
    [searchTimeout, fetchUsers],
  )

  // Load users on component mount
  useEffect(() => {
    fetchUsers()
  }, [fetchUsers])

  // Cleanup timeout on unmount
  useEffect(() => {
    return () => {
      if (searchTimeout) {
        clearTimeout(searchTimeout)
      }
    }
  }, [searchTimeout])

  // Fetch tickets when owner changes
  useEffect(() => {
    fetchTickets(selectedOwner)
    // Reset ticketIds when owner changes
    setValue('ticketIds', [])
  }, [selectedOwner, setValue, fetchTickets])

  // Type guard for Option
  function isOption(obj: any): obj is Option {
    return obj && typeof obj === 'object' && 'value' in obj
  }

  const onSubmit = async (data: FormValues) => {

    console.log('data', data)

    return

    setSubmitting(true)
    try {
      const response = await fetch('/api/admin/ticket-gifts', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ownerId: parseInt(data.ownerId),
          ticketIds: data.ticketIds,
          recipientFirstName: data.recipientFirstName,
          recipientLastName: data.recipientLastName,
          recipientEmail: data.recipientEmail,
          recipientPhone: data.recipientPhone,
          message: data.message,
        }),
      })

      const result = await response.json()

      if (response.ok) {
        toast.success('Gift created successfully! Notification email sent to recipient.')
        reset()
        setSelectedOwner('')
        setTickets([])
      } else {
        toast.error(result.message || 'Failed to create gift')
      }
    } catch (error) {
      console.error('Error creating gift:', error)
      toast.error('Failed to create gift')
    } finally {
      setSubmitting(false)
    }
  }

  return (
    <Gutter>
      <div style={{ maxWidth: '800px', margin: '0 auto' }}>
        <h2 style={{ fontSize: '24px', fontWeight: 'bold', marginBottom: '24px' }}>
          Gift Tickets to Another User
        </h2>

        <form
          onSubmit={handleSubmit(onSubmit)}
          style={{ display: 'flex', flexDirection: 'column', gap: '24px' }}
        >
          {/* Ticket Owner Section */}
          <div>
            <h3 style={{ fontWeight: '600', marginBottom: '12px' }}>1. Select Ticket Owner</h3>
            {/* Using Controller for better form control with PayloadCMS SelectInput */}
            <Controller
              name="ownerId"
              control={control}
              render={({ field }) => (
                <SelectInput
                  label="Ticket Owner"
                  name="ownerId"
                  path="ownerId"
                  value={field.value}
                  options={users.map((user) => ({
                    label: `${user.email}${user.firstName && user.lastName ? ` (${user.firstName} ${user.lastName})` : ''}`,
                    value: user.id.toString(),
                  }))}
                  onChange={(option) => {
                    // Payload SelectInput returns Option or Option[]
                    if (isOption(option)) {
                      field.onChange(option.value)
                      setSelectedOwner(option.value)
                    } else if (Array.isArray(option) && option.length > 0 && isOption(option[0])) {
                      field.onChange(option[0].value)
                      setSelectedOwner(option[0].value)
                    } else {
                      field.onChange('')
                      setSelectedOwner('')
                    }
                  }}
                  onInputChange={(inputValue) => {
                    // Use debounced search to avoid too many API calls
                    if (inputValue && inputValue.length > 2) {
                      debouncedSearch(inputValue)
                    } else if (!inputValue) {
                      fetchUsers()
                    }
                  }}
                  Error={errors.ownerId?.message}
                  required
                  placeholder={loadingUsers ? 'Loading users...' : 'Search by email...'}
                  isClearable
                />
              )}
            />
          </div>

          {/* Ticket Selection Section */}
          {selectedOwner && (
            <div>
              <h3 style={{ fontWeight: '600', marginBottom: '12px' }}>
                2. Select Ticket(s) to Gift
              </h3>
              <div style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>
                {loadingTickets && (
                  <div style={{ color: '#666', fontStyle: 'italic' }}>Loading tickets...</div>
                )}
                {!loadingTickets && tickets.length === 0 && (
                  <div style={{ color: '#666' }}>No giftable tickets found for this user.</div>
                )}
                {/* Using Controller for checkbox array handling */}
                <Controller
                  name="ticketIds"
                  control={control}
                  render={({ field }) => (
                    <div
                      style={{
                        display: 'grid',
                        gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))',
                        gap: '16px',
                      }}
                    >
                      {!loadingTickets &&
                        tickets.map((ticket) => (
                          <CheckboxInput
                            key={ticket.id}
                            label={`${ticket.ticketCode}${ticket.seat ? ` - Seat ${ticket.seat}` : ''} - ${ticket.event?.title || 'Unknown Event'}${ticket.ticketPriceName ? ` (${ticket.ticketPriceName})` : ''}`}
                            name="ticketIds"
                            checked={field.value.includes(ticket.id)}
                            onToggle={(event) => {
                              const checked = event.target.checked
                              let newValue = [...field.value]
                              if (checked) {
                                newValue.push(ticket.id)
                              } else {
                                newValue = newValue.filter((id) => id !== ticket.id)
                              }
                              field.onChange(newValue)
                            }}
                          />
                        ))}
                    </div>
                  )}
                />

                {errors.ticketIds?.message && (
                  <div style={{ color: '#dc3545', fontSize: '14px', marginTop: '4px' }}>
                    {errors.ticketIds.message}
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Recipient Information Section */}
          <div>
            <h3 style={{ fontWeight: '600', marginBottom: '12px' }}>3. Recipient Information</h3>
            <div
              style={{
                display: 'grid',
                gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',
                gap: '16px',
              }}
            >
              <Controller
                name="recipientFirstName"
                control={control}
                render={({ field }) => (
                  <TextInput
                    label="First Name"
                    placeholder="First Name"
                    path="recipientFirstName"
                    value={field.value}
                    onChange={field.onChange}
                    Error={errors.recipientFirstName?.message}
                    required
                  />
                )}
              />
              <Controller
                name="recipientLastName"
                control={control}
                render={({ field }) => (
                  <TextInput
                    label="Last Name"
                    placeholder="Last Name"
                    path="recipientLastName"
                    value={field.value}
                    onChange={field.onChange}
                    Error={errors.recipientLastName?.message}
                    required
                  />
                )}
              />
            </div>
            <div
              style={{
                display: 'grid',
                gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',
                gap: '16px',
                marginTop: '16px',
              }}
            >
              <Controller
                name="recipientEmail"
                control={control}
                render={({ field }) => (
                  <TextInput
                    label="Email"
                    placeholder="Email"
                    path="recipientEmail"
                    value={field.value}
                    onChange={field.onChange}
                    Error={errors.recipientEmail?.message}
                    required
                  />
                )}
              />
              <Controller
                name="recipientPhone"
                control={control}
                render={({ field }) => (
                  <TextInput
                    label="Phone Number"
                    placeholder="Phone Number (optional)"
                    path="recipientPhone"
                    value={field.value}
                    onChange={field.onChange}
                    Error={errors.recipientPhone?.message}
                  />
                )}
              />
            </div>
          </div>

          {/* Message Section */}
          <div>
            <h3 style={{ fontWeight: '600', marginBottom: '12px' }}>
              4. Personal Message (Optional)
            </h3>
            <Controller
              name="message"
              control={control}
              render={({ field }) => (
                <TextInput
                  label="Message"
                  placeholder="Add a personal message to your gift..."
                  path="message"
                  value={field.value}
                  onChange={field.onChange}
                  Error={errors.message?.message}
                />
              )}
            />
          </div>

          <div style={{ width: '100%', marginTop: '16px' }}>
            <Button
              type="submit"
              disabled={submitting || isSubmitting || !selectedOwner || (watch('ticketIds') || []).length === 0}
            >
              {submitting || isSubmitting ? 'Sending Gift...' : 'Send Gift'}
            </Button>
          </div>
        </form>
      </div>
    </Gutter>
  )
}

export default CreateTicketGiftForm
