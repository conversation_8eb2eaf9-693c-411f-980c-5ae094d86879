'use client'

import React, { useEffect, useState, useCallback } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { Button, TextInput, SelectInput, CheckboxInput, Gutter, toast } from '@payloadcms/ui'

// Option type for Payload SelectInput
interface Option {
  label: string
  value: string
}

// Types for API responses
interface User {
  id: number
  email: string
  firstName?: string
  lastName?: string
}

interface Ticket {
  id: number
  ticketCode: string
  seat?: string
  attendeeName?: string
  status: string
  event?: {
    id: number
    title: string
    eventDate?: string
  }
  ticketPriceName?: string
  createdAt: string
}

// Validation schema using zod
const schema = z.object({
  ownerId: z.string().min(1, 'Ticket owner is required'),
  ticketIds: z.array(z.number()).min(1, 'Select at least one ticket'),
  recipientFirstName: z.string().min(1, 'First name is required'),
  recipientLastName: z.string().min(1, 'Last name is required'),
  recipientEmail: z.string().min(1, 'Email is required').email('Invalid email'),
  recipientPhone: z.string().optional(),
  message: z.string().optional(),
})

type FormValues = z.infer<typeof schema>

const CreateTicketGiftForm: React.FC = () => {
  const [users, setUsers] = useState<User[]>([])
  const [tickets, setTickets] = useState<Ticket[]>([])
  const [selectedOwner, setSelectedOwner] = useState<string>('')
  const [loadingUsers, setLoadingUsers] = useState(false)
  const [loadingTickets, setLoadingTickets] = useState(false)
  const [submitting, setSubmitting] = useState(false)

  const {
    register,
    handleSubmit,
    setValue,
    watch,
    formState: { errors, isSubmitting },
    reset,
  } = useForm<FormValues>({
    resolver: zodResolver(schema),
    defaultValues: {
      ownerId: '',
      ticketIds: [],
      recipientFirstName: '',
      recipientLastName: '',
      recipientEmail: '',
      recipientPhone: '',
      message: '',
    },
  })

  // Fetch users from API
  const fetchUsers = useCallback(async (search = '') => {
    setLoadingUsers(true)
    try {
      const response = await fetch(`/api/admin/users?search=${encodeURIComponent(search)}&limit=50`)
      if (response.ok) {
        const data = await response.json()
        setUsers(data.data.docs || [])
      } else {
        toast.error('Failed to fetch users')
      }
    } catch (error) {
      console.error('Error fetching users:', error)
      toast.error('Failed to fetch users')
    } finally {
      setLoadingUsers(false)
    }
  }, [])

  // Fetch tickets for selected owner
  const fetchTickets = useCallback(async (userId: string) => {
    if (!userId) {
      setTickets([])
      return
    }

    setLoadingTickets(true)
    try {
      const response = await fetch(`/api/admin/users/${userId}/tickets`)
      if (response.ok) {
        const data = await response.json()
        setTickets(data.data.docs || [])
      } else {
        toast.error('Failed to fetch tickets')
        setTickets([])
      }
    } catch (error) {
      console.error('Error fetching tickets:', error)
      toast.error('Failed to fetch tickets')
      setTickets([])
    } finally {
      setLoadingTickets(false)
    }
  }, [])

  // Load users on component mount
  useEffect(() => {
    fetchUsers()
  }, [fetchUsers])

  // Fetch tickets when owner changes
  useEffect(() => {
    fetchTickets(selectedOwner)
    // Reset ticketIds when owner changes
    setValue('ticketIds', [])
  }, [selectedOwner, setValue, fetchTickets])

  const ownerId = watch('ownerId')
  const ticketIds = watch('ticketIds') || []

  // Type guard for Option
  function isOption(obj: any): obj is Option {
    return obj && typeof obj === 'object' && 'value' in obj
  }

  const onSubmit = async (data: FormValues) => {
    setSubmitting(true)
    try {
      const response = await fetch('/api/admin/ticket-gifts', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ownerId: parseInt(data.ownerId),
          ticketIds: data.ticketIds,
          recipientFirstName: data.recipientFirstName,
          recipientLastName: data.recipientLastName,
          recipientEmail: data.recipientEmail,
          recipientPhone: data.recipientPhone,
          message: data.message,
        }),
      })

      const result = await response.json()

      if (response.ok) {
        toast.success('Gift created successfully! Notification email sent to recipient.')
        reset()
        setSelectedOwner('')
        setTickets([])
      } else {
        toast.error(result.message || 'Failed to create gift')
      }
    } catch (error) {
      console.error('Error creating gift:', error)
      toast.error('Failed to create gift')
    } finally {
      setSubmitting(false)
    }
  }

  return (
    <Gutter>
      <div style={{ maxWidth: '800px', margin: '0 auto' }}>
        <h2 style={{ fontSize: '24px', fontWeight: 'bold', marginBottom: '24px' }}>
          Gift Tickets to Another User
        </h2>

        <form onSubmit={handleSubmit(onSubmit)} style={{ display: 'flex', flexDirection: 'column', gap: '24px' }}>
          {/* Ticket Owner Section */}
          <div>
            <h3 style={{ fontWeight: '600', marginBottom: '12px' }}>1. Select Ticket Owner</h3>
            <SelectInput
              label="Ticket Owner"
              name="ownerId"
              path="ownerId"
              value={ownerId}
              options={users.map((user) => ({
                label: `${user.email}${user.firstName && user.lastName ? ` (${user.firstName} ${user.lastName})` : ''}`,
                value: user.id.toString(),
              }))}
              onChange={(option) => {
                // Payload SelectInput returns Option or Option[]
                if (isOption(option)) {
                  setValue('ownerId', option.value)
                  setSelectedOwner(option.value)
                } else if (Array.isArray(option) && option.length > 0 && isOption(option[0])) {
                  setValue('ownerId', option[0].value)
                  setSelectedOwner(option[0].value)
                } else {
                  setValue('ownerId', '')
                  setSelectedOwner('')
                }
              }}
              Error={errors.ownerId?.message}
              required
              placeholder={loadingUsers ? "Loading users..." : "Select a user..."}
              // disabled={loadingUsers}
            />
          </div>

          {/* Ticket Selection Section */}
          {selectedOwner && (
            <div>
              <h3 style={{ fontWeight: '600', marginBottom: '12px' }}>2. Select Ticket(s) to Gift</h3>
              <div style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>
                {loadingTickets && (
                  <div style={{ color: '#666', fontStyle: 'italic' }}>Loading tickets...</div>
                )}
                {!loadingTickets && tickets.length === 0 && (
                  <div style={{ color: '#666' }}>No giftable tickets found for this user.</div>
                )}
                {!loadingTickets && tickets.map((ticket) => (
                  <CheckboxInput
                    key={ticket.id}
                    label={`${ticket.ticketCode}${ticket.seat ? ` - Seat ${ticket.seat}` : ''} - ${ticket.event?.title || 'Unknown Event'}${ticket.ticketPriceName ? ` (${ticket.ticketPriceName})` : ''}`}
                    name="ticketIds"
                    checked={ticketIds.includes(ticket.id)}
                    onToggle={(event) => {
                      const checked = event.target.checked
                      let newValue = [...ticketIds]
                      if (checked) {
                        newValue.push(ticket.id)
                      } else {
                        newValue = newValue.filter((id) => id !== ticket.id)
                      }
                      setValue('ticketIds', newValue)
                    }}
                  />
                ))}
                {errors.ticketIds?.message && ticketIds.length === 0 && (
                  <div style={{ color: '#dc3545', fontSize: '14px', marginTop: '4px' }}>
                    {errors.ticketIds.message}
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Recipient Information Section */}
          <div>
            <h3 style={{ fontWeight: '600', marginBottom: '12px' }}>3. Recipient Information</h3>
            <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))', gap: '16px' }}>
              <TextInput
                label="First Name"
                placeholder="First Name"
                path="recipientFirstName"
                {...register('recipientFirstName')}
                Error={errors.recipientFirstName?.message}
                required
              />
              <TextInput
                label="Last Name"
                placeholder="Last Name"
                path="recipientLastName"
                {...register('recipientLastName')}
                Error={errors.recipientLastName?.message}
                required
              />
            </div>
            <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))', gap: '16px', marginTop: '16px' }}>
              <TextInput
                label="Email"
                placeholder="Email"
                path="recipientEmail"
                {...register('recipientEmail')}
                Error={errors.recipientEmail?.message}
                required
              />
              <TextInput
                label="Phone Number"
                placeholder="Phone Number (optional)"
                path="recipientPhone"
                {...register('recipientPhone')}
                Error={errors.recipientPhone?.message}
              />
            </div>
          </div>

          {/* Message Section */}
          <div>
            <h3 style={{ fontWeight: '600', marginBottom: '12px' }}>4. Personal Message (Optional)</h3>
            <TextInput
              label="Message"
              placeholder="Add a personal message to your gift..."
              path="message"
              {...register('message')}
              Error={errors.message?.message}
            />
          </div>

          <Button
            type="submit"
            disabled={submitting || isSubmitting || !selectedOwner || ticketIds.length === 0}
            // style={{ width: '100%', marginTop: '16px' }}
          >
            {submitting || isSubmitting ? 'Sending Gift...' : 'Send Gift'}
          </Button>
        </form>
      </div>
    </Gutter>
  )
}

export default CreateTicketGiftForm
