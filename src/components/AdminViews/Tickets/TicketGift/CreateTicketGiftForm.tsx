import React, { useEffect, useState } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { Button, TextInput, SelectInput, CheckboxInput, Gutter, toast } from '@payloadcms/ui'

// Option type for Payload SelectInput
interface Option {
  label: string
  value: string
}

// Mocked user and ticket data
const mockUsers = [
  { id: 1, email: '<EMAIL>', firstName: 'Alice', lastName: 'Smith' },
  { id: 2, email: '<EMAIL>', firstName: 'Bob', lastName: 'Johnson' },
]

const mockTickets = {
  1: [
    { id: 't1', ticketCode: 'A123', event: 'Concert A', status: 'active' },
    { id: 't2', ticketCode: 'A124', event: 'Concert B', status: 'active' },
  ],
  2: [{ id: 't3', ticketCode: 'B123', event: 'Concert C', status: 'active' }],
}

// Validation schema using zod
const schema = z.object({
  ownerId: z.string().min(1, 'Ticket owner is required'),
  ticketIds: z.array(z.string()).min(1, 'Select at least one ticket'),
  recipientFirstName: z.string().min(1, 'First name is required'),
  recipientLastName: z.string().min(1, 'Last name is required'),
  recipientEmail: z.string().min(1, 'Email is required').email('Invalid email'),
  recipientPhone: z.string().optional(),
})

type FormValues = z.infer<typeof schema>

const CreateTicketGiftForm: React.FC = () => {
  const [users, setUsers] = useState<typeof mockUsers>([])
  const [tickets, setTickets] = useState<any[]>([])
  const [selectedOwner, setSelectedOwner] = useState<string>('')
  const [submitted, setSubmitted] = useState<any>(null)

  const {
    register,
    handleSubmit,
    setValue,
    watch,
    formState: { errors },
    reset,
  } = useForm<FormValues>({
    resolver: zodResolver(schema),
    defaultValues: {
      ownerId: '',
      ticketIds: [],
      recipientFirstName: '',
      recipientLastName: '',
      recipientEmail: '',
      recipientPhone: '',
    },
  })

  // Mock fetch users
  useEffect(() => {
    setTimeout(() => setUsers(mockUsers), 300)
  }, [])

  // Fetch tickets when owner changes
  useEffect(() => {
    if (selectedOwner) {
      setTimeout(() => setTickets(mockTickets[selectedOwner] || []), 300)
    } else {
      setTickets([])
    }
    // Reset ticketIds when owner changes
    setValue('ticketIds', [])
  }, [selectedOwner, setValue])

  const ownerId = watch('ownerId')
  const ticketIds = watch('ticketIds') || []

  // Type guard for Option
  function isOption(obj: any): obj is Option {
    return obj && typeof obj === 'object' && 'value' in obj
  }

  const onSubmit = (data: FormValues) => {
    setSubmitted(data)
    toast.success('Gift sent!')
    reset()
    setSelectedOwner('')
  }

  return (
    <div className="max-w-xl mx-auto p-6 bg-white rounded shadow">
      <h2 className="text-2xl font-bold mb-6">Gift Tickets to Another User</h2>
      <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
        {/* Ticket Owner Section */}
        <div>
          <h3 className="font-semibold mb-2">1. Select Ticket Owner</h3>
          <SelectInput
            label="Ticket Owner"
            name="ownerId"
            path="ownerId"
            value={ownerId}
            options={users.map((user) => ({
              label: `${user.email} (${user.firstName} ${user.lastName})`,
              value: user.id.toString(),
            }))}
            onChange={(option) => {
              // Payload SelectInput returns Option or Option[]
              if (isOption(option)) {
                setValue('ownerId', option.value)
                setSelectedOwner(option.value)
              } else if (Array.isArray(option) && option.length > 0 && isOption(option[0])) {
                setValue('ownerId', option[0].value)
                setSelectedOwner(option[0].value)
              } else {
                setValue('ownerId', '')
                setSelectedOwner('')
              }
            }}
            Error={errors.ownerId?.message}
            required
            placeholder="Select a user..."
          />
        </div>

        {/* Ticket Selection Section */}
        {selectedOwner && (
          <div>
            <h3 className="font-semibold mb-2">2. Select Ticket(s) to Gift</h3>
            <div className="flex flex-col gap-2">
              {tickets.length === 0 && (
                <span className="text-gray-500">No tickets found for this user.</span>
              )}
              {tickets.map((ticket) => (
                <CheckboxInput
                  key={ticket.id}
                  label={`${ticket.ticketCode} - ${ticket.event} (${ticket.status})`}
                  name="ticketIds"
                  checked={ticketIds.includes(ticket.id)}
                  onToggle={(event) => {
                    const checked = event.target.checked
                    let newValue = [...ticketIds]
                    if (checked) {
                      newValue.push(ticket.id)
                    } else {
                      newValue = newValue.filter((id) => id !== ticket.id)
                    }
                    setValue('ticketIds', newValue)
                  }}
                />
              ))}
              {errors.ticketIds?.message && ticketIds.length === 0 && (
                <div className="text-red-600 text-sm mt-1">{errors.ticketIds.message}</div>
              )}
            </div>
          </div>
        )}

        {/* Recipient Information Section */}
        <div>
          <h3 className="font-semibold mb-2">3. Recipient Information</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <TextInput
              label="First Name"
              placeholder="First Name"
              path="recipientFirstName"
              {...register('recipientFirstName')}
              Error={errors.recipientFirstName?.message}
              required
            />
            <TextInput
              label="Last Name"
              placeholder="Last Name"
              path="recipientLastName"
              {...register('recipientLastName')}
              Error={errors.recipientLastName?.message}
              required
            />
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
            <TextInput
              label="Email"
              placeholder="Email"
              path="recipientEmail"
              {...register('recipientEmail')}
              Error={errors.recipientEmail?.message}
              required
            />
            <TextInput
              label="Phone Number"
              placeholder="Phone Number (optional)"
              path="recipientPhone"
              {...register('recipientPhone')}
              Error={errors.recipientPhone?.message}
            />
          </div>
        </div>

        <Button type="submit" className="w-full mt-4">
          Send Gift
        </Button>
      </form>
      {submitted && (
        <div className="mt-8 p-4 bg-gray-100 rounded">
          <h4 className="font-semibold mb-2">Submission JSON</h4>
          <pre className="text-xs bg-gray-200 p-2 rounded overflow-x-auto">
            {JSON.stringify(submitted, null, 2)}
          </pre>
        </div>
      )}
    </div>
  )
}

export default CreateTicketGiftForm
