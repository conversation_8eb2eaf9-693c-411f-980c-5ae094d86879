import { Payload } from 'payload'
import { TICKET_STATUS } from '@/collections/Tickets/constants'

interface TransferTicketsParams {
  ticketIds: number[]
  fromUserId: number
  toUserId: number
  newAttendeeName: string
  payload: Payload
  transactionId?: string | number
}

interface TransferResult {
  success: boolean
  transferredTickets: number[]
  errors: string[]
}

/**
 * Transfer tickets from one user to another
 * This function handles the core logic of ticket ownership transfer
 */
export async function transferTickets({
  ticketIds,
  fromUserId,
  toUserId,
  newAttendeeName,
  payload,
  transactionId,
}: TransferTicketsParams): Promise<TransferResult> {
  const result: TransferResult = {
    success: false,
    transferredTickets: [],
    errors: [],
  }

  try {
    // Validate that all tickets exist and belong to the original owner
    const tickets = await payload.find({
      collection: 'tickets',
      where: {
        id: { in: ticketIds },
        user: { equals: fromUserId },
        status: { equals: TICKET_STATUS.booked.value },
      },
      depth: 0,
    })

    if (tickets.docs.length !== ticketIds.length) {
      const foundTicketIds = tickets.docs.map(t => t.id)
      const missingTicketIds = ticketIds.filter(id => !foundTicketIds.includes(id))
      result.errors.push(`Some tickets are not found, not owned by user, or not transferable: ${missingTicketIds.join(', ')}`)
      return result
    }

    // Check if any tickets are already checked in
    const checkedInTickets = await payload.find({
      collection: 'checkinRecords',
      where: {
        ticket: { in: ticketIds },
        deletedAt: { equals: null },
      },
      limit: ticketIds.length,
    })

    if (checkedInTickets.docs.length > 0) {
      const checkedInTicketIds = checkedInTickets.docs.map(record => 
        typeof record.ticket === 'object' ? record.ticket.id : record.ticket
      )
      result.errors.push(`Cannot transfer tickets that have already been checked in: ${checkedInTicketIds.join(', ')}`)
      return result
    }

    // Verify the recipient user exists
    const recipientUser = await payload.findByID({
      collection: 'users',
      id: toUserId,
    })

    if (!recipientUser) {
      result.errors.push('Recipient user not found')
      return result
    }

    // Transfer each ticket
    const transferPromises = ticketIds.map(async (ticketId) => {
      try {
        await payload.update({
          collection: 'tickets',
          id: ticketId,
          data: {
            user: toUserId,
            attendeeName: newAttendeeName,
          },
        })
        return ticketId
      } catch (error) {
        console.error(`Failed to transfer ticket ${ticketId}:`, error)
        result.errors.push(`Failed to transfer ticket ${ticketId}: ${error.message}`)
        return null
      }
    })

    const transferResults = await Promise.all(transferPromises)
    result.transferredTickets = transferResults.filter(id => id !== null) as number[]

    if (result.transferredTickets.length === ticketIds.length) {
      result.success = true
    } else {
      result.errors.push(`Only ${result.transferredTickets.length} out of ${ticketIds.length} tickets were transferred successfully`)
    }

    return result
  } catch (error) {
    console.error('Error in transferTickets:', error)
    result.errors.push(`Transfer failed: ${error.message}`)
    return result
  }
}

/**
 * Validate if tickets can be gifted
 */
export async function validateTicketsForGifting(
  ticketIds: number[],
  ownerId: number,
  payload: Payload
): Promise<{ valid: boolean; errors: string[] }> {
  const errors: string[] = []

  try {
    // Check if tickets exist and belong to owner
    const tickets = await payload.find({
      collection: 'tickets',
      where: {
        id: { in: ticketIds },
        user: { equals: ownerId },
        status: { equals: TICKET_STATUS.booked.value },
      },
      depth: 1,
    })

    if (tickets.docs.length !== ticketIds.length) {
      errors.push('Some tickets are not found, not owned by user, or not in booked status')
    }

    // Check if any tickets are from past events
    const now = new Date()
    for (const ticket of tickets.docs) {
      if (ticket.eventDate && new Date(ticket.eventDate) < now) {
        errors.push(`Ticket ${ticket.ticketCode} is for a past event and cannot be gifted`)
      }
    }

    // Check if any tickets are already checked in
    const checkedInTickets = await payload.find({
      collection: 'checkinRecords',
      where: {
        ticket: { in: ticketIds },
        deletedAt: { equals: null },
      },
      limit: ticketIds.length,
    })

    if (checkedInTickets.docs.length > 0) {
      errors.push('Some tickets have already been checked in and cannot be gifted')
    }

    // Check if any tickets are already part of a pending gift
    const existingGifts = await payload.find({
      collection: 'ticket-gifts',
      where: {
        tickets: { in: ticketIds },
        status: { equals: 'pending' },
      },
      limit: ticketIds.length,
    })

    if (existingGifts.docs.length > 0) {
      errors.push('Some tickets are already part of a pending gift')
    }

    return {
      valid: errors.length === 0,
      errors,
    }
  } catch (error) {
    console.error('Error validating tickets for gifting:', error)
    return {
      valid: false,
      errors: [`Validation failed: ${error.message}`],
    }
  }
}

/**
 * Create or find user account for gift recipient
 */
export async function createOrFindRecipientUser(
  email: string,
  firstName: string,
  lastName: string,
  phone: string | undefined,
  payload: Payload
): Promise<{ userId: number; isNewUser: boolean; error?: string }> {
  try {
    // First, try to find existing user
    const existingUsers = await payload.find({
      collection: 'users',
      where: { email: { equals: email } },
      limit: 1,
    })

    if (existingUsers.docs.length > 0) {
      return {
        userId: existingUsers.docs[0].id,
        isNewUser: false,
      }
    }

    // Create new user account
    const newUser = await payload.create({
      collection: 'users',
      data: {
        email,
        firstName,
        lastName,
        phoneNumber: phone || '',
        role: 'customer',
        // Set a temporary password - user will need to reset it
        password: Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15),
      },
    })

    return {
      userId: newUser.id,
      isNewUser: true,
    }
  } catch (error) {
    console.error('Error creating/finding recipient user:', error)
    return {
      userId: 0,
      isNewUser: false,
      error: `Failed to create/find user: ${error.message}`,
    }
  }
}
