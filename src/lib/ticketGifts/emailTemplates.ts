interface GiftNotificationEmailParams {
  recipientName: string
  senderName: string
  ticketCount: number
  giftCode: string
  message: string
  acceptUrl: string
  ticketDetails?: Array<{
    ticketCode: string
    eventTitle: string
    seat?: string
    eventDate?: string
  }>
}

interface GiftAcceptedEmailParams {
  senderName: string
  recipientName: string
  ticketCount: number
  giftCode: string
  ticketDetails?: Array<{
    ticketCode: string
    eventTitle: string
    seat?: string
    eventDate?: string
  }>
}

interface GiftRejectedEmailParams {
  senderName: string
  recipientName: string
  ticketCount: number
  giftCode: string
}

export function generateGiftNotificationEmail({
  recipientName,
  senderName,
  ticketCount,
  giftCode,
  message,
  acceptUrl,
  ticketDetails = [],
}: GiftNotificationEmailParams): string {
  const ticketDetailsHtml = ticketDetails.length > 0 ? `
    <div style="background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;">
      <h3 style="margin-top: 0; color: #495057;">Ticket Details:</h3>
      ${ticketDetails.map(ticket => `
        <div style="margin-bottom: 12px; padding: 12px; background: white; border-radius: 4px;">
          <strong>${ticket.eventTitle}</strong><br>
          <span style="color: #666;">Ticket: ${ticket.ticketCode}</span>
          ${ticket.seat ? `<br><span style="color: #666;">Seat: ${ticket.seat}</span>` : ''}
          ${ticket.eventDate ? `<br><span style="color: #666;">Date: ${new Date(ticket.eventDate).toLocaleDateString()}</span>` : ''}
        </div>
      `).join('')}
    </div>
  ` : ''

  return `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Ticket Gift Notification</title>
    </head>
    <body style="font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 0; background-color: #f8f9fa;">
      <div style="max-width: 600px; margin: 0 auto; background: white; box-shadow: 0 0 10px rgba(0,0,0,0.1);">
        <!-- Header -->
        <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px 20px; text-align: center;">
          <h1 style="margin: 0; font-size: 28px; font-weight: 300;">🎫 You've Received a Gift!</h1>
        </div>
        
        <!-- Content -->
        <div style="padding: 30px 20px;">
          <p style="font-size: 18px; margin-bottom: 20px;">Hi ${recipientName},</p>
          
          <p style="font-size: 16px; margin-bottom: 20px;">
            <strong>${senderName}</strong> has gifted you <strong>${ticketCount} ticket${ticketCount > 1 ? 's' : ''}</strong>!
          </p>
          
          ${message ? `
            <div style="background: #e3f2fd; padding: 20px; border-left: 4px solid #2196f3; margin: 25px 0; border-radius: 4px;">
              <p style="margin: 0; font-weight: 600; color: #1976d2; margin-bottom: 8px;">Message from ${senderName}:</p>
              <p style="margin: 0; font-style: italic; color: #424242;">"${message}"</p>
            </div>
          ` : ''}
          
          ${ticketDetailsHtml}
          
          <div style="background: #f5f5f5; padding: 20px; border-radius: 8px; margin: 25px 0; text-align: center;">
            <p style="margin: 0 0 10px 0; font-weight: 600; color: #333;">Gift Code:</p>
            <code style="background: #fff; padding: 8px 16px; border-radius: 4px; font-family: 'Courier New', monospace; font-size: 16px; color: #d32f2f; border: 2px dashed #ddd;">${giftCode}</code>
          </div>
          
          <p style="margin: 25px 0;">To accept your gift and claim your tickets, click the button below:</p>
          
          <div style="text-align: center; margin: 30px 0;">
            <a href="${acceptUrl}" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 15px 40px; text-decoration: none; border-radius: 50px; display: inline-block; font-weight: 600; font-size: 16px; box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4); transition: all 0.3s ease;">Accept Gift</a>
          </div>
          
          <div style="background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 4px; margin: 25px 0;">
            <p style="margin: 0; font-size: 14px; color: #856404;">
              <strong>⏰ Important:</strong> This gift will expire in 7 days. Make sure to accept it before then!
            </p>
          </div>
        </div>
        
        <!-- Footer -->
        <div style="background: #f8f9fa; padding: 20px; border-top: 1px solid #dee2e6; font-size: 12px; color: #6c757d;">
          <p style="margin: 0 0 10px 0;">If you're having trouble with the button above, copy and paste this URL into your browser:</p>
          <p style="margin: 0; word-break: break-all;">${acceptUrl}</p>
        </div>
      </div>
    </body>
    </html>
  `
}

export function generateGiftAcceptedEmail({
  senderName,
  recipientName,
  ticketCount,
  giftCode,
  ticketDetails = [],
}: GiftAcceptedEmailParams): string {
  const ticketDetailsHtml = ticketDetails.length > 0 ? `
    <div style="background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;">
      <h3 style="margin-top: 0; color: #495057;">Transferred Tickets:</h3>
      ${ticketDetails.map(ticket => `
        <div style="margin-bottom: 12px; padding: 12px; background: white; border-radius: 4px;">
          <strong>${ticket.eventTitle}</strong><br>
          <span style="color: #666;">Ticket: ${ticket.ticketCode}</span>
          ${ticket.seat ? `<br><span style="color: #666;">Seat: ${ticket.seat}</span>` : ''}
          ${ticket.eventDate ? `<br><span style="color: #666;">Date: ${new Date(ticket.eventDate).toLocaleDateString()}</span>` : ''}
        </div>
      `).join('')}
    </div>
  ` : ''

  return `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Gift Accepted</title>
    </head>
    <body style="font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 0; background-color: #f8f9fa;">
      <div style="max-width: 600px; margin: 0 auto; background: white; box-shadow: 0 0 10px rgba(0,0,0,0.1);">
        <!-- Header -->
        <div style="background: linear-gradient(135deg, #28a745 0%, #20c997 100%); color: white; padding: 30px 20px; text-align: center;">
          <h1 style="margin: 0; font-size: 28px; font-weight: 300;">✅ Your Gift Was Accepted!</h1>
        </div>
        
        <!-- Content -->
        <div style="padding: 30px 20px;">
          <p style="font-size: 18px; margin-bottom: 20px;">Hi ${senderName},</p>
          
          <p style="font-size: 16px; margin-bottom: 20px;">
            Great news! <strong>${recipientName}</strong> has accepted your gift of <strong>${ticketCount} ticket${ticketCount > 1 ? 's' : ''}</strong>.
          </p>
          
          <div style="background: #d4edda; border: 1px solid #c3e6cb; padding: 20px; border-radius: 8px; margin: 25px 0;">
            <p style="margin: 0; color: #155724;">
              <strong>Gift Code:</strong> ${giftCode}<br>
              <strong>Status:</strong> Accepted ✅<br>
              <strong>Tickets transferred to:</strong> ${recipientName}
            </p>
          </div>
          
          ${ticketDetailsHtml}
          
          <p style="margin: 25px 0;">The tickets have been successfully transferred to ${recipientName}'s account. They can now use these tickets to attend the event(s).</p>
        </div>
        
        <!-- Footer -->
        <div style="background: #f8f9fa; padding: 20px; border-top: 1px solid #dee2e6; font-size: 12px; color: #6c757d; text-align: center;">
          <p style="margin: 0;">Thank you for using our ticket gifting service!</p>
        </div>
      </div>
    </body>
    </html>
  `
}

export function generateGiftRejectedEmail({
  senderName,
  recipientName,
  ticketCount,
  giftCode,
}: GiftRejectedEmailParams): string {
  return `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Gift Declined</title>
    </head>
    <body style="font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 0; background-color: #f8f9fa;">
      <div style="max-width: 600px; margin: 0 auto; background: white; box-shadow: 0 0 10px rgba(0,0,0,0.1);">
        <!-- Header -->
        <div style="background: linear-gradient(135deg, #dc3545 0%, #c82333 100%); color: white; padding: 30px 20px; text-align: center;">
          <h1 style="margin: 0; font-size: 28px; font-weight: 300;">❌ Gift Declined</h1>
        </div>
        
        <!-- Content -->
        <div style="padding: 30px 20px;">
          <p style="font-size: 18px; margin-bottom: 20px;">Hi ${senderName},</p>
          
          <p style="font-size: 16px; margin-bottom: 20px;">
            We wanted to let you know that <strong>${recipientName}</strong> has declined your gift of <strong>${ticketCount} ticket${ticketCount > 1 ? 's' : ''}</strong>.
          </p>
          
          <div style="background: #f8d7da; border: 1px solid #f5c6cb; padding: 20px; border-radius: 8px; margin: 25px 0;">
            <p style="margin: 0; color: #721c24;">
              <strong>Gift Code:</strong> ${giftCode}<br>
              <strong>Status:</strong> Declined ❌<br>
              <strong>Tickets remain with:</strong> ${senderName}
            </p>
          </div>
          
          <p style="margin: 25px 0;">
            Don't worry! The tickets are still in your account and you can:
          </p>
          
          <ul style="margin: 20px 0; padding-left: 20px;">
            <li>Use the tickets yourself</li>
            <li>Gift them to someone else</li>
            <li>Keep them for future use</li>
          </ul>
        </div>
        
        <!-- Footer -->
        <div style="background: #f8f9fa; padding: 20px; border-top: 1px solid #dee2e6; font-size: 12px; color: #6c757d; text-align: center;">
          <p style="margin: 0;">Thank you for using our ticket gifting service!</p>
        </div>
      </div>
    </body>
    </html>
  `
}
