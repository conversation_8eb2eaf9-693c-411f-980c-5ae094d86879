'use client'

import React, { useEffect, useState } from 'react'
import { useParams } from 'next/navigation'

interface GiftData {
  giftCode: string
  recipientFirstName: string
  recipientLastName: string
  recipientEmail: string
  message: string
  tickets: Array<{
    id: number
    ticketCode: string
    seat?: string
    attendeeName?: string
    ticketPriceName?: string
    event?: {
      id: number
      title: string
      eventDate?: string
    }
  }>
  sender: {
    firstName?: string
    lastName?: string
    email: string
  }
  expiresAt: string
  createdAt: string
}

export default function GiftAcceptPage() {
  const params = useParams()
  const giftCode = params.giftCode as string
  
  const [gift, setGift] = useState<GiftData | null>(null)
  const [loading, setLoading] = useState(true)
  const [processing, setProcessing] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [success, setSuccess] = useState<string | null>(null)

  useEffect(() => {
    if (giftCode) {
      fetchGift()
    }
  }, [giftCode])

  const fetchGift = async () => {
    try {
      const response = await fetch(`/api/gift/${giftCode}`)
      const data = await response.json()

      if (response.ok) {
        setGift(data.data)
      } else {
        setError(data.message || 'Failed to load gift')
      }
    } catch (err) {
      setError('Failed to load gift')
    } finally {
      setLoading(false)
    }
  }

  const handleAction = async (action: 'accept' | 'reject') => {
    setProcessing(true)
    setError(null)

    try {
      const response = await fetch(`/api/gift/${giftCode}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ action }),
      })

      const data = await response.json()

      if (response.ok) {
        setSuccess(data.message)
        setGift(null) // Hide the gift form
      } else {
        setError(data.message || `Failed to ${action} gift`)
      }
    } catch (err) {
      setError(`Failed to ${action} gift`)
    } finally {
      setProcessing(false)
    }
  }

  if (loading) {
    return (
      <div style={{ 
        minHeight: '100vh', 
        display: 'flex', 
        alignItems: 'center', 
        justifyContent: 'center',
        fontFamily: 'system-ui, -apple-system, sans-serif'
      }}>
        <div>Loading gift...</div>
      </div>
    )
  }

  if (error && !gift) {
    return (
      <div style={{ 
        minHeight: '100vh', 
        display: 'flex', 
        alignItems: 'center', 
        justifyContent: 'center',
        fontFamily: 'system-ui, -apple-system, sans-serif'
      }}>
        <div style={{
          maxWidth: '500px',
          padding: '40px',
          textAlign: 'center',
          backgroundColor: '#f8f9fa',
          borderRadius: '8px',
          boxShadow: '0 2px 10px rgba(0,0,0,0.1)'
        }}>
          <h1 style={{ color: '#dc3545', marginBottom: '20px' }}>❌ Gift Not Available</h1>
          <p style={{ color: '#666', fontSize: '16px' }}>{error}</p>
        </div>
      </div>
    )
  }

  if (success) {
    return (
      <div style={{ 
        minHeight: '100vh', 
        display: 'flex', 
        alignItems: 'center', 
        justifyContent: 'center',
        fontFamily: 'system-ui, -apple-system, sans-serif'
      }}>
        <div style={{
          maxWidth: '500px',
          padding: '40px',
          textAlign: 'center',
          backgroundColor: '#f8f9fa',
          borderRadius: '8px',
          boxShadow: '0 2px 10px rgba(0,0,0,0.1)'
        }}>
          <h1 style={{ color: '#28a745', marginBottom: '20px' }}>✅ Success!</h1>
          <p style={{ color: '#666', fontSize: '16px' }}>{success}</p>
        </div>
      </div>
    )
  }

  if (!gift) return null

  const senderName = `${gift.sender.firstName || ''} ${gift.sender.lastName || ''}`.trim() || gift.sender.email
  const expiresAt = new Date(gift.expiresAt)
  const isExpired = new Date() > expiresAt

  return (
    <div style={{ 
      minHeight: '100vh', 
      backgroundColor: '#f8f9fa',
      fontFamily: 'system-ui, -apple-system, sans-serif',
      padding: '20px'
    }}>
      <div style={{
        maxWidth: '800px',
        margin: '0 auto',
        backgroundColor: 'white',
        borderRadius: '12px',
        boxShadow: '0 4px 20px rgba(0,0,0,0.1)',
        overflow: 'hidden'
      }}>
        {/* Header */}
        <div style={{
          background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
          color: 'white',
          padding: '40px 30px',
          textAlign: 'center'
        }}>
          <h1 style={{ margin: '0 0 10px 0', fontSize: '32px', fontWeight: '300' }}>
            🎫 You've Received a Gift!
          </h1>
          <p style={{ margin: 0, fontSize: '18px', opacity: 0.9 }}>
            From {senderName}
          </p>
        </div>

        {/* Content */}
        <div style={{ padding: '40px 30px' }}>
          {error && (
            <div style={{
              backgroundColor: '#f8d7da',
              color: '#721c24',
              padding: '15px',
              borderRadius: '8px',
              marginBottom: '20px',
              border: '1px solid #f5c6cb'
            }}>
              {error}
            </div>
          )}

          <div style={{ marginBottom: '30px' }}>
            <h2 style={{ color: '#333', marginBottom: '15px' }}>Gift Details</h2>
            <div style={{
              backgroundColor: '#f8f9fa',
              padding: '20px',
              borderRadius: '8px',
              marginBottom: '20px'
            }}>
              <p><strong>Recipient:</strong> {gift.recipientFirstName} {gift.recipientLastName}</p>
              <p><strong>Email:</strong> {gift.recipientEmail}</p>
              <p><strong>Gift Code:</strong> <code style={{ backgroundColor: '#e9ecef', padding: '4px 8px', borderRadius: '4px' }}>{gift.giftCode}</code></p>
              <p><strong>Expires:</strong> {expiresAt.toLocaleDateString()} at {expiresAt.toLocaleTimeString()}</p>
            </div>

            {gift.message && (
              <div style={{
                backgroundColor: '#e3f2fd',
                padding: '20px',
                borderLeft: '4px solid #2196f3',
                borderRadius: '4px',
                marginBottom: '20px'
              }}>
                <p style={{ margin: '0 0 8px 0', fontWeight: '600', color: '#1976d2' }}>
                  Message from {senderName}:
                </p>
                <p style={{ margin: 0, fontStyle: 'italic', color: '#424242' }}>
                  "{gift.message}"
                </p>
              </div>
            )}
          </div>

          <div style={{ marginBottom: '30px' }}>
            <h2 style={{ color: '#333', marginBottom: '15px' }}>
              Tickets ({gift.tickets.length})
            </h2>
            <div style={{ display: 'grid', gap: '15px' }}>
              {gift.tickets.map((ticket) => (
                <div key={ticket.id} style={{
                  backgroundColor: '#f8f9fa',
                  padding: '20px',
                  borderRadius: '8px',
                  border: '1px solid #dee2e6'
                }}>
                  <h3 style={{ margin: '0 0 10px 0', color: '#495057' }}>
                    {ticket.event?.title || 'Unknown Event'}
                  </h3>
                  <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', gap: '10px', fontSize: '14px', color: '#666' }}>
                    <div><strong>Ticket:</strong> {ticket.ticketCode}</div>
                    {ticket.seat && <div><strong>Seat:</strong> {ticket.seat}</div>}
                    {ticket.ticketPriceName && <div><strong>Type:</strong> {ticket.ticketPriceName}</div>}
                    {ticket.event?.eventDate && (
                      <div><strong>Date:</strong> {new Date(ticket.event.eventDate).toLocaleDateString()}</div>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </div>

          {!isExpired && (
            <div style={{ display: 'flex', gap: '15px', justifyContent: 'center' }}>
              <button
                onClick={() => handleAction('accept')}
                disabled={processing}
                style={{
                  backgroundColor: '#28a745',
                  color: 'white',
                  border: 'none',
                  padding: '15px 30px',
                  borderRadius: '8px',
                  fontSize: '16px',
                  fontWeight: '600',
                  cursor: processing ? 'not-allowed' : 'pointer',
                  opacity: processing ? 0.6 : 1
                }}
              >
                {processing ? 'Processing...' : 'Accept Gift'}
              </button>
              <button
                onClick={() => handleAction('reject')}
                disabled={processing}
                style={{
                  backgroundColor: '#dc3545',
                  color: 'white',
                  border: 'none',
                  padding: '15px 30px',
                  borderRadius: '8px',
                  fontSize: '16px',
                  fontWeight: '600',
                  cursor: processing ? 'not-allowed' : 'pointer',
                  opacity: processing ? 0.6 : 1
                }}
              >
                {processing ? 'Processing...' : 'Decline Gift'}
              </button>
            </div>
          )}

          {isExpired && (
            <div style={{
              backgroundColor: '#fff3cd',
              color: '#856404',
              padding: '20px',
              borderRadius: '8px',
              textAlign: 'center',
              border: '1px solid #ffeaa7'
            }}>
              <strong>⏰ This gift has expired and can no longer be accepted.</strong>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
