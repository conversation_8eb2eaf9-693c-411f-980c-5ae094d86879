import { NextRequest, NextResponse } from 'next/server'
import { getPayload } from 'payload'
import { transferTickets, createOrFindRecipientUser } from '@/lib/ticketGifts/transferTickets'
import { generateGiftAcceptedEmail, generateGiftRejectedEmail } from '@/lib/ticketGifts/emailTemplates'
import { sendMailAndWriteLog } from '@/collections/Emails/utils'

export async function GET(
  req: NextRequest,
  { params }: { params: { giftCode: string } }
) {
  try {
    const payload = await getPayload()
    const { giftCode } = params

    if (!giftCode) {
      return NextResponse.json({ message: 'Gift code is required' }, { status: 400 })
    }

    // Find the gift
    const gifts = await payload.find({
      collection: 'ticket-gifts',
      where: { giftCode: { equals: giftCode } },
      depth: 2, // Include tickets and event details
      limit: 1,
    })

    if (!gifts.docs.length) {
      return NextResponse.json({ message: 'Gift not found' }, { status: 404 })
    }

    const gift = gifts.docs[0]

    // Check if gift is expired
    if (new Date() > new Date(gift.expiresAt)) {
      // Update status to expired if not already
      if (gift.status === 'pending') {
        await payload.update({
          collection: 'ticket-gifts',
          id: gift.id,
          data: { status: 'expired' },
        })
      }
      return NextResponse.json({ message: 'Gift has expired' }, { status: 410 })
    }

    // Check if gift is already processed
    if (gift.status !== 'pending') {
      return NextResponse.json({ 
        message: `Gift has already been ${gift.status}`,
        status: gift.status 
      }, { status: 409 })
    }

    // Get sender information
    const sender = await payload.findByID({
      collection: 'users',
      id: typeof gift.originalOwner === 'object' ? gift.originalOwner.id : gift.originalOwner,
    })

    // Transform ticket data
    const tickets = Array.isArray(gift.tickets) ? gift.tickets.map((ticket: any) => ({
      id: ticket.id,
      ticketCode: ticket.ticketCode,
      seat: ticket.seat,
      attendeeName: ticket.attendeeName,
      ticketPriceName: ticket.ticketPriceName,
      event: ticket.event ? {
        id: typeof ticket.event === 'object' ? ticket.event.id : ticket.event,
        title: typeof ticket.event === 'object' ? ticket.event.title : 'Unknown Event',
        eventDate: ticket.eventDate,
      } : null,
    })) : []

    return NextResponse.json({
      success: true,
      data: {
        giftCode: gift.giftCode,
        recipientFirstName: gift.recipientFirstName,
        recipientLastName: gift.recipientLastName,
        recipientEmail: gift.recipientEmail,
        message: gift.message,
        tickets,
        sender: {
          firstName: sender.firstName,
          lastName: sender.lastName,
          email: sender.email,
        },
        expiresAt: gift.expiresAt,
        createdAt: gift.createdAt,
      },
    })
  } catch (error) {
    console.error('Error fetching gift:', error)
    return NextResponse.json(
      { message: 'Failed to fetch gift', error: error.message },
      { status: 500 }
    )
  }
}

export async function POST(
  req: NextRequest,
  { params }: { params: { giftCode: string } }
) {
  try {
    const payload = await getPayload()
    const { giftCode } = params
    const { action, userInfo } = await req.json()

    if (!giftCode || !action) {
      return NextResponse.json({ message: 'Gift code and action are required' }, { status: 400 })
    }

    if (!['accept', 'reject'].includes(action)) {
      return NextResponse.json({ message: 'Invalid action' }, { status: 400 })
    }

    // Find the gift
    const gifts = await payload.find({
      collection: 'ticket-gifts',
      where: { giftCode: { equals: giftCode } },
      depth: 1,
      limit: 1,
    })

    if (!gifts.docs.length) {
      return NextResponse.json({ message: 'Gift not found' }, { status: 404 })
    }

    const gift = gifts.docs[0]

    // Check if gift is expired
    if (new Date() > new Date(gift.expiresAt)) {
      return NextResponse.json({ message: 'Gift has expired' }, { status: 410 })
    }

    // Check if gift is already processed
    if (gift.status !== 'pending') {
      return NextResponse.json({ 
        message: `Gift has already been ${gift.status}` 
      }, { status: 409 })
    }

    const transactionId = await payload.db.beginTransaction()

    try {
      if (action === 'accept') {
        // Handle gift acceptance
        let recipientUserId = gift.recipientUser

        // Create or find recipient user
        if (!recipientUserId) {
          const userResult = await createOrFindRecipientUser(
            gift.recipientEmail,
            gift.recipientFirstName,
            gift.recipientLastName,
            gift.recipientPhone,
            payload
          )

          if (userResult.error) {
            throw new Error(userResult.error)
          }

          recipientUserId = userResult.userId
        }

        // Transfer tickets to recipient
        const ticketIds = Array.isArray(gift.tickets)
          ? gift.tickets.map((t: any) => typeof t === 'object' ? t.id : t)
          : []

        const originalOwnerId = typeof gift.originalOwner === 'object'
          ? gift.originalOwner.id
          : gift.originalOwner

        const transferResult = await transferTickets({
          ticketIds,
          fromUserId: originalOwnerId,
          toUserId: recipientUserId,
          newAttendeeName: `${gift.recipientFirstName} ${gift.recipientLastName}`,
          payload,
        })

        if (!transferResult.success) {
          throw new Error(`Transfer failed: ${transferResult.errors.join('; ')}`)
        }

        // Update gift status
        await payload.update({
          collection: 'ticket-gifts',
          id: gift.id,
          data: {
            status: 'accepted',
            recipientUser: recipientUserId,
            acceptedAt: new Date().toISOString(),
          },
        })

        await payload.db.commitTransaction(transactionId)

        // Send confirmation email to the original sender
        try {
          const senderUser = await payload.findByID({
            collection: 'users',
            id: originalOwnerId,
          })

          const ticketDetails = Array.isArray(gift.tickets)
            ? gift.tickets.map((ticket: any) => ({
                ticketCode: ticket.ticketCode || '',
                eventTitle: typeof ticket.event === 'object' ? ticket.event.title : 'Unknown Event',
                seat: ticket.seat,
                eventDate: ticket.eventDate,
              }))
            : []

          const emailHtml = generateGiftAcceptedEmail({
            senderName: `${senderUser.firstName || ''} ${senderUser.lastName || ''}`.trim() || senderUser.email,
            recipientName: `${gift.recipientFirstName} ${gift.recipientLastName}`,
            ticketCount: ticketIds.length,
            giftCode: gift.giftCode,
            ticketDetails,
          })

          await sendMailAndWriteLog({
            to: senderUser.email,
            subject: `✅ Your ticket gift was accepted by ${gift.recipientFirstName} ${gift.recipientLastName}`,
            html: emailHtml,
            payload,
          })
        } catch (emailError) {
          console.error('Failed to send gift accepted email:', emailError)
          // Don't fail the operation if email fails
        }

        return NextResponse.json({
          success: true,
          message: 'Gift accepted successfully! Tickets have been transferred to your account.',
        })
      } else {
        // Handle gift rejection
        await payload.update({
          collection: 'ticket-gifts',
          id: gift.id,
          data: {
            status: 'rejected',
            rejectedAt: new Date().toISOString(),
          },
        })

        await payload.db.commitTransaction(transactionId)

        // Send notification email to the original sender
        try {
          const senderUser = await payload.findByID({
            collection: 'users',
            id: typeof gift.originalOwner === 'object' ? gift.originalOwner.id : gift.originalOwner,
          })

          const emailHtml = generateGiftRejectedEmail({
            senderName: `${senderUser.firstName || ''} ${senderUser.lastName || ''}`.trim() || senderUser.email,
            recipientName: `${gift.recipientFirstName} ${gift.recipientLastName}`,
            ticketCount: Array.isArray(gift.tickets) ? gift.tickets.length : 0,
            giftCode: gift.giftCode,
          })

          await sendMailAndWriteLog({
            to: senderUser.email,
            subject: `❌ Your ticket gift was declined by ${gift.recipientFirstName} ${gift.recipientLastName}`,
            html: emailHtml,
            payload,
          })
        } catch (emailError) {
          console.error('Failed to send gift rejected email:', emailError)
          // Don't fail the operation if email fails
        }

        return NextResponse.json({
          success: true,
          message: 'Gift rejected successfully.',
        })
      }
    } catch (error) {
      await payload.db.rollbackTransaction(transactionId)
      throw error
    }
  } catch (error) {
    console.error('Error processing gift:', error)
    return NextResponse.json(
      { message: 'Failed to process gift', error: error.message },
      { status: 500 }
    )
  }
}
