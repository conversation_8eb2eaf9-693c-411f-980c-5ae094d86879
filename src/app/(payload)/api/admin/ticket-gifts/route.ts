import { NextRequest, NextResponse } from 'next/server'
import { getPayload } from 'payload'
import { sendMailAndWriteLog } from '@/collections/Emails/utils'
import { validateTicketsForGifting } from '@/lib/ticketGifts/transferTickets'
import { generateGiftNotificationEmail } from '@/lib/ticketGifts/emailTemplates'

interface CreateGiftRequest {
  ownerId: number
  ticketIds: number[]
  recipientFirstName: string
  recipientLastName: string
  recipientEmail: string
  recipientPhone?: string
  message?: string
}

export async function POST(req: NextRequest) {
  try {
    const payload = await getPayload()
    const body: CreateGiftRequest = await req.json()

    const {
      ownerId,
      ticketIds,
      recipientFirstName,
      recipientLastName,
      recipientEmail,
      recipientPhone,
      message,
    } = body

    // Validate required fields
    if (!ownerId || !ticketIds?.length || !recipientFirstName || !recipientLastName || !recipientEmail) {
      return NextResponse.json(
        { message: 'Missing required fields' },
        { status: 400 }
      )
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    if (!emailRegex.test(recipientEmail)) {
      return NextResponse.json(
        { message: 'Invalid email format' },
        { status: 400 }
      )
    }

    // Start transaction
    const transactionId = await payload.db.beginTransaction()

    try {
      // Validate tickets for gifting
      const validation = await validateTicketsForGifting(ticketIds, ownerId, payload)
      if (!validation.valid) {
        throw new Error(validation.errors.join('; '))
      }

      // Get ticket details for email
      const tickets = await payload.find({
        collection: 'tickets',
        where: {
          id: { in: ticketIds },
        },
        depth: 1,
      })

      // Check if recipient already has an account
      const existingUser = await payload.find({
        collection: 'users',
        where: { email: { equals: recipientEmail } },
        limit: 1,
      })

      const recipientUser = existingUser.docs[0] || null

      // Create the gift record
      const gift = await payload.create({
        collection: 'ticket-gifts',
        data: {
          originalOwner: ownerId,
          recipientEmail,
          recipientFirstName,
          recipientLastName,
          recipientPhone,
          recipientUser: recipientUser?.id || null,
          tickets: ticketIds,
          status: 'pending',
          message: message || '',
        },
      })

      // Send email notification to recipient
      try {
        const ownerUser = await payload.findByID({
          collection: 'users',
          id: ownerId,
        })

        const senderName = `${ownerUser.firstName || ''} ${ownerUser.lastName || ''}`.trim() || ownerUser.email
        const emailSubject = `🎫 You've received a ticket gift from ${senderName}!`

        // Prepare ticket details for email
        const ticketDetails = tickets.docs.map(ticket => ({
          ticketCode: ticket.ticketCode || '',
          eventTitle: typeof ticket.event === 'object' ? ticket.event.title : 'Unknown Event',
          seat: ticket.seat,
          eventDate: ticket.eventDate,
        }))

        const emailHtml = generateGiftNotificationEmail({
          recipientName: `${recipientFirstName} ${recipientLastName}`,
          senderName,
          ticketCount: tickets.docs.length,
          giftCode: gift.giftCode,
          message: message || '',
          acceptUrl: `${process.env.NEXT_PUBLIC_APP_URL}/gift/accept/${gift.giftCode}`,
          ticketDetails,
        })

        await sendMailAndWriteLog({
          to: recipientEmail,
          subject: emailSubject,
          html: emailHtml,
          payload,
        })
      } catch (emailError) {
        console.error('Failed to send gift notification email:', emailError)
        // Don't fail the entire operation if email fails
      }

      await payload.db.commitTransaction(transactionId)

      return NextResponse.json({
        success: true,
        data: {
          giftId: gift.id,
          giftCode: gift.giftCode,
          message: 'Gift created successfully and notification sent',
        },
      })
    } catch (error) {
      await payload.db.rollbackTransaction(transactionId)
      throw error
    }
  } catch (error) {
    console.error('Error creating ticket gift:', error)
    return NextResponse.json(
      { message: 'Failed to create ticket gift', error: error.message },
      { status: 500 }
    )
  }
}


