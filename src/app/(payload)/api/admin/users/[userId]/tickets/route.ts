import { NextRequest, NextResponse } from 'next/server'
import { getPayload } from 'payload'
import { TICKET_STATUS } from '@/collections/Tickets/constants'

export async function GET(
  req: NextRequest,
  { params }: { params: { userId: string } }
) {
  try {
    const payload = await getPayload()
    const { userId } = params

    if (!userId) {
      return NextResponse.json({ message: 'User ID is required' }, { status: 400 })
    }

    // Get query parameters
    const searchParams = req.nextUrl.searchParams
    const page = parseInt(searchParams.get('page') || '1', 10)
    const limit = parseInt(searchParams.get('limit') || '50', 10)

    // Only fetch tickets that can be gifted (booked status)
    const tickets = await payload.find({
      collection: 'tickets',
      where: {
        user: { equals: parseInt(userId) },
        status: { equals: TICKET_STATUS.booked.value },
      },
      page,
      limit,
      sort: '-createdAt',
      depth: 1, // Include event details
    })

    // Transform the data to include event information
    const transformedTickets = tickets.docs.map((ticket) => ({
      id: ticket.id,
      ticketCode: ticket.ticketCode,
      seat: ticket.seat,
      attendeeName: ticket.attendeeName,
      status: ticket.status,
      event: ticket.event ? {
        id: typeof ticket.event === 'object' ? ticket.event.id : ticket.event,
        title: typeof ticket.event === 'object' ? ticket.event.title : 'Unknown Event',
        slug: typeof ticket.event === 'object' ? ticket.event.slug : '',
        eventDate: ticket.eventDate,
      } : null,
      ticketPriceName: ticket.ticketPriceName,
      createdAt: ticket.createdAt,
    }))

    return NextResponse.json({
      success: true,
      data: {
        docs: transformedTickets,
        totalDocs: tickets.totalDocs,
        totalPages: tickets.totalPages,
        page: tickets.page,
        limit: tickets.limit,
        hasNextPage: tickets.hasNextPage,
        hasPrevPage: tickets.hasPrevPage,
      },
    })
  } catch (error) {
    console.error('Error fetching user tickets:', error)
    return NextResponse.json(
      { message: 'Failed to fetch user tickets', error: error.message },
      { status: 500 }
    )
  }
}
