import { NextRequest, NextResponse } from 'next/server'
import { getPayload } from 'payload'
import { isAdminOrSuperAdmin } from '@/access/isAdminOrSuperAdmin'

export async function GET(req: NextRequest) {
  try {
    const payload = await getPayload()
    
    // Check if user is admin
    const user = req.headers.get('authorization')
    if (!user) {
      return NextResponse.json({ message: 'Unauthorized' }, { status: 401 })
    }

    // Get query parameters
    const searchParams = req.nextUrl.searchParams
    const search = searchParams.get('search') || ''
    const page = parseInt(searchParams.get('page') || '1', 10)
    const limit = parseInt(searchParams.get('limit') || '20', 10)

    // Build where clause for search
    const where: any = {}
    if (search) {
      where.or = [
        { email: { contains: search } },
        { firstName: { contains: search } },
        { lastName: { contains: search } },
      ]
    }

    // Fetch users
    const users = await payload.find({
      collection: 'users',
      where,
      page,
      limit,
      sort: 'email',
      select: {
        id: true,
        email: true,
        firstName: true,
        lastName: true,
      },
    })

    return NextResponse.json({
      success: true,
      data: users,
    })
  } catch (error) {
    console.error('Error fetching users:', error)
    return NextResponse.json(
      { message: 'Failed to fetch users', error: error.message },
      { status: 500 }
    )
  }
}
