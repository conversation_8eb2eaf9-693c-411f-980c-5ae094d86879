/* tslint:disable */
/* eslint-disable */
/**
 * This file was automatically generated by Payload.
 * DO NOT MODIFY IT BY HAND. Instead, modify your source Payload config,
 * and re-run `payload generate:types` to regenerate this file.
 */

/**
 * Supported timezones in IANA format.
 *
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "supportedTimezones".
 */
export type SupportedTimezones =
  | 'Pacific/Midway'
  | 'Pacific/Niue'
  | 'Pacific/Honolulu'
  | 'Pacific/Rarotonga'
  | 'America/Anchorage'
  | 'Pacific/Gambier'
  | 'America/Los_Angeles'
  | 'America/Tijuana'
  | 'America/Denver'
  | 'America/Phoenix'
  | 'America/Chicago'
  | 'America/Guatemala'
  | 'America/New_York'
  | 'America/Bogota'
  | 'America/Caracas'
  | 'America/Santiago'
  | 'America/Buenos_Aires'
  | 'America/Sao_Paulo'
  | 'Atlantic/South_Georgia'
  | 'Atlantic/Azores'
  | 'Atlantic/Cape_Verde'
  | 'Europe/London'
  | 'Europe/Berlin'
  | 'Africa/Lagos'
  | 'Europe/Athens'
  | 'Africa/Cairo'
  | 'Europe/Moscow'
  | 'Asia/Riyadh'
  | 'Asia/Dubai'
  | 'Asia/Baku'
  | 'Asia/Karachi'
  | 'Asia/Tashkent'
  | 'Asia/Calcutta'
  | 'Asia/Dhaka'
  | 'Asia/Almaty'
  | 'Asia/Jakarta'
  | 'Asia/Bangkok'
  | 'Asia/Shanghai'
  | 'Asia/Singapore'
  | 'Asia/Tokyo'
  | 'Asia/Seoul'
  | 'Australia/Brisbane'
  | 'Australia/Sydney'
  | 'Pacific/Guam'
  | 'Pacific/Noumea'
  | 'Pacific/Auckland'
  | 'Pacific/Fiji';

export interface Config {
  auth: {
    admins: AdminAuthOperations;
  };
  blocks: {};
  collections: {
    pages: Page;
    posts: Post;
    media: Media;
    categories: Category;
    users: User;
    memberships: Membership;
    'membership-rank-configs': MembershipRankConfig;
    'membership-gifts': MembershipGift;
    'membership-histories': MembershipHistory;
    checkinRecords: CheckinRecord;
    seatingCharts: SeatingChart;
    events: Event;
    promotions: Promotion;
    promotionConfigs: PromotionConfig;
    userPromotionRedemptions: UserPromotionRedemption;
    orders: Order;
    orderItems: OrderItem;
    payments: Payment;
    tickets: Ticket;
    'ticket-gifts': TicketGift;
    seatHoldings: SeatHolding;
    partners: Partner;
    performers: Performer;
    activities: Activity;
    faqs: Faq;
    admins: Admin;
    emails: Email;
    'affiliate-ranks': AffiliateRank;
    'event-affiliate-ranks': EventAffiliateRank;
    'affiliate-user-ranks': AffiliateUserRank;
    'event-affiliate-user-ranks': EventAffiliateUserRank;
    'affiliate-rank-logs': AffiliateRankLog;
    'affiliate-links': AffiliateLink;
    'affiliate-settings': AffiliateSetting;
    'affiliate-click-logs': AffiliateClickLog;
    marketingTrackings: MarketingTracking;
    logs: Log;
    redirects: Redirect;
    forms: Form;
    'form-submissions': FormSubmission;
    search: Search;
    exports: Export;
    'payload-jobs': PayloadJob;
    'payload-locked-documents': PayloadLockedDocument;
    'payload-preferences': PayloadPreference;
    'payload-migrations': PayloadMigration;
  };
  collectionsJoins: {};
  collectionsSelect: {
    pages: PagesSelect<false> | PagesSelect<true>;
    posts: PostsSelect<false> | PostsSelect<true>;
    media: MediaSelect<false> | MediaSelect<true>;
    categories: CategoriesSelect<false> | CategoriesSelect<true>;
    users: UsersSelect<false> | UsersSelect<true>;
    memberships: MembershipsSelect<false> | MembershipsSelect<true>;
    'membership-rank-configs': MembershipRankConfigsSelect<false> | MembershipRankConfigsSelect<true>;
    'membership-gifts': MembershipGiftsSelect<false> | MembershipGiftsSelect<true>;
    'membership-histories': MembershipHistoriesSelect<false> | MembershipHistoriesSelect<true>;
    checkinRecords: CheckinRecordsSelect<false> | CheckinRecordsSelect<true>;
    seatingCharts: SeatingChartsSelect<false> | SeatingChartsSelect<true>;
    events: EventsSelect<false> | EventsSelect<true>;
    promotions: PromotionsSelect<false> | PromotionsSelect<true>;
    promotionConfigs: PromotionConfigsSelect<false> | PromotionConfigsSelect<true>;
    userPromotionRedemptions: UserPromotionRedemptionsSelect<false> | UserPromotionRedemptionsSelect<true>;
    orders: OrdersSelect<false> | OrdersSelect<true>;
    orderItems: OrderItemsSelect<false> | OrderItemsSelect<true>;
    payments: PaymentsSelect<false> | PaymentsSelect<true>;
    tickets: TicketsSelect<false> | TicketsSelect<true>;
    'ticket-gifts': TicketGiftsSelect<false> | TicketGiftsSelect<true>;
    seatHoldings: SeatHoldingsSelect<false> | SeatHoldingsSelect<true>;
    partners: PartnersSelect<false> | PartnersSelect<true>;
    performers: PerformersSelect<false> | PerformersSelect<true>;
    activities: ActivitiesSelect<false> | ActivitiesSelect<true>;
    faqs: FaqsSelect<false> | FaqsSelect<true>;
    admins: AdminsSelect<false> | AdminsSelect<true>;
    emails: EmailsSelect<false> | EmailsSelect<true>;
    'affiliate-ranks': AffiliateRanksSelect<false> | AffiliateRanksSelect<true>;
    'event-affiliate-ranks': EventAffiliateRanksSelect<false> | EventAffiliateRanksSelect<true>;
    'affiliate-user-ranks': AffiliateUserRanksSelect<false> | AffiliateUserRanksSelect<true>;
    'event-affiliate-user-ranks': EventAffiliateUserRanksSelect<false> | EventAffiliateUserRanksSelect<true>;
    'affiliate-rank-logs': AffiliateRankLogsSelect<false> | AffiliateRankLogsSelect<true>;
    'affiliate-links': AffiliateLinksSelect<false> | AffiliateLinksSelect<true>;
    'affiliate-settings': AffiliateSettingsSelect<false> | AffiliateSettingsSelect<true>;
    'affiliate-click-logs': AffiliateClickLogsSelect<false> | AffiliateClickLogsSelect<true>;
    marketingTrackings: MarketingTrackingsSelect<false> | MarketingTrackingsSelect<true>;
    logs: LogsSelect<false> | LogsSelect<true>;
    redirects: RedirectsSelect<false> | RedirectsSelect<true>;
    forms: FormsSelect<false> | FormsSelect<true>;
    'form-submissions': FormSubmissionsSelect<false> | FormSubmissionsSelect<true>;
    search: SearchSelect<false> | SearchSelect<true>;
    exports: ExportsSelect<false> | ExportsSelect<true>;
    'payload-jobs': PayloadJobsSelect<false> | PayloadJobsSelect<true>;
    'payload-locked-documents': PayloadLockedDocumentsSelect<false> | PayloadLockedDocumentsSelect<true>;
    'payload-preferences': PayloadPreferencesSelect<false> | PayloadPreferencesSelect<true>;
    'payload-migrations': PayloadMigrationsSelect<false> | PayloadMigrationsSelect<true>;
  };
  db: {
    defaultIDType: number;
  };
  globals: {
    header: Header;
    footer: Footer;
  };
  globalsSelect: {
    header: HeaderSelect<false> | HeaderSelect<true>;
    footer: FooterSelect<false> | FooterSelect<true>;
  };
  locale: 'en' | 'vi';
  user: Admin & {
    collection: 'admins';
  };
  jobs: {
    tasks: {
      createCollectionExport: TaskCreateCollectionExport;
      schedulePublish: TaskSchedulePublish;
      inline: {
        input: unknown;
        output: unknown;
      };
    };
    workflows: unknown;
  };
}
export interface AdminAuthOperations {
  forgotPassword: {
    email: string;
    password: string;
  };
  login: {
    email: string;
    password: string;
  };
  registerFirstUser: {
    email: string;
    password: string;
  };
  unlock: {
    email: string;
    password: string;
  };
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "pages".
 */
export interface Page {
  id: number;
  banner?: (number | null) | Media;
  title: string;
  description?: string | null;
  hero: {
    type: 'none' | 'highImpact' | 'mediumImpact' | 'lowImpact';
    richText?: {
      root: {
        type: string;
        children: {
          type: string;
          version: number;
          [k: string]: unknown;
        }[];
        direction: ('ltr' | 'rtl') | null;
        format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | '';
        indent: number;
        version: number;
      };
      [k: string]: unknown;
    } | null;
    links?:
      | {
          link: {
            type?: ('reference' | 'custom') | null;
            newTab?: boolean | null;
            reference?:
              | ({
                  relationTo: 'pages';
                  value: number | Page;
                } | null)
              | ({
                  relationTo: 'posts';
                  value: number | Post;
                } | null);
            url?: string | null;
            label: string;
            /**
             * Choose how the link should be rendered.
             */
            appearance?: ('default' | 'outline') | null;
          };
          id?: string | null;
        }[]
      | null;
    media?: (number | null) | Media;
  };
  layout: (CallToActionBlock | ContentBlock | MediaBlock | ArchiveBlock | FormBlock | CardsBlock | CardDetailBlock)[];
  meta?: {
    title?: string | null;
    /**
     * Maximum upload file size: 12MB. Recommended file size for images is <500KB.
     */
    image?: (number | null) | Media;
    description?: string | null;
  };
  publishedAt?: string | null;
  slug?: string | null;
  slugLock?: boolean | null;
  parent?: (number | null) | Page;
  breadcrumbs?:
    | {
        doc?: (number | null) | Page;
        url?: string | null;
        label?: string | null;
        id?: string | null;
      }[]
    | null;
  updatedAt: string;
  createdAt: string;
  _status?: ('draft' | 'published') | null;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "media".
 */
export interface Media {
  id: number;
  alt?: string | null;
  caption?: {
    root: {
      type: string;
      children: {
        type: string;
        version: number;
        [k: string]: unknown;
      }[];
      direction: ('ltr' | 'rtl') | null;
      format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | '';
      indent: number;
      version: number;
    };
    [k: string]: unknown;
  } | null;
  updatedAt: string;
  createdAt: string;
  url?: string | null;
  thumbnailURL?: string | null;
  filename?: string | null;
  mimeType?: string | null;
  filesize?: number | null;
  width?: number | null;
  height?: number | null;
  focalX?: number | null;
  focalY?: number | null;
  sizes?: {
    thumbnail?: {
      url?: string | null;
      width?: number | null;
      height?: number | null;
      mimeType?: string | null;
      filesize?: number | null;
      filename?: string | null;
    };
    square?: {
      url?: string | null;
      width?: number | null;
      height?: number | null;
      mimeType?: string | null;
      filesize?: number | null;
      filename?: string | null;
    };
    small?: {
      url?: string | null;
      width?: number | null;
      height?: number | null;
      mimeType?: string | null;
      filesize?: number | null;
      filename?: string | null;
    };
    medium?: {
      url?: string | null;
      width?: number | null;
      height?: number | null;
      mimeType?: string | null;
      filesize?: number | null;
      filename?: string | null;
    };
    large?: {
      url?: string | null;
      width?: number | null;
      height?: number | null;
      mimeType?: string | null;
      filesize?: number | null;
      filename?: string | null;
    };
    xlarge?: {
      url?: string | null;
      width?: number | null;
      height?: number | null;
      mimeType?: string | null;
      filesize?: number | null;
      filename?: string | null;
    };
    og?: {
      url?: string | null;
      width?: number | null;
      height?: number | null;
      mimeType?: string | null;
      filesize?: number | null;
      filename?: string | null;
    };
  };
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "posts".
 */
export interface Post {
  id: number;
  title: string;
  heroImage?: (number | null) | Media;
  content: {
    root: {
      type: string;
      children: {
        type: string;
        version: number;
        [k: string]: unknown;
      }[];
      direction: ('ltr' | 'rtl') | null;
      format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | '';
      indent: number;
      version: number;
    };
    [k: string]: unknown;
  };
  relatedPosts?: (number | Post)[] | null;
  categories?: (number | Category)[] | null;
  meta?: {
    title?: string | null;
    /**
     * Maximum upload file size: 12MB. Recommended file size for images is <500KB.
     */
    image?: (number | null) | Media;
    description?: string | null;
  };
  publishedAt?: string | null;
  authors?: (number | User)[] | null;
  populatedAuthors?:
    | {
        id?: string | null;
        name?: string | null;
      }[]
    | null;
  slug?: string | null;
  slugLock?: boolean | null;
  updatedAt: string;
  createdAt: string;
  _status?: ('draft' | 'published') | null;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "categories".
 */
export interface Category {
  id: number;
  title: string;
  slug?: string | null;
  slugLock?: boolean | null;
  parent?: (number | null) | Category;
  breadcrumbs?:
    | {
        doc?: (number | null) | Category;
        url?: string | null;
        label?: string | null;
        id?: string | null;
      }[]
    | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "users".
 */
export interface User {
  id: number;
  email: string;
  salt?: string | null;
  hash?: string | null;
  resetPasswordToken?: string | null;
  resetPasswordExpiration?: string | null;
  phoneNumber?: string | null;
  phoneNumbers?:
    | {
        phone?: string | null;
        createdAt?: string | null;
        isUsing?: boolean | null;
        id?: string | null;
      }[]
    | null;
  username?: string | null;
  firstName?: string | null;
  lastName?: string | null;
  lastActive?: string | null;
  role?: ('affiliate' | 'user') | null;
  affiliateStatus?: ('pending' | 'approved' | 'rejected') | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "CallToActionBlock".
 */
export interface CallToActionBlock {
  richText?: {
    root: {
      type: string;
      children: {
        type: string;
        version: number;
        [k: string]: unknown;
      }[];
      direction: ('ltr' | 'rtl') | null;
      format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | '';
      indent: number;
      version: number;
    };
    [k: string]: unknown;
  } | null;
  links?:
    | {
        link: {
          type?: ('reference' | 'custom') | null;
          newTab?: boolean | null;
          reference?:
            | ({
                relationTo: 'pages';
                value: number | Page;
              } | null)
            | ({
                relationTo: 'posts';
                value: number | Post;
              } | null);
          url?: string | null;
          label: string;
          /**
           * Choose how the link should be rendered.
           */
          appearance?: ('default' | 'outline') | null;
        };
        id?: string | null;
      }[]
    | null;
  id?: string | null;
  blockName?: string | null;
  blockType: 'cta';
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "ContentBlock".
 */
export interface ContentBlock {
  columns?:
    | {
        size?: ('oneThird' | 'half' | 'twoThirds' | 'full') | null;
        richText?: {
          root: {
            type: string;
            children: {
              type: string;
              version: number;
              [k: string]: unknown;
            }[];
            direction: ('ltr' | 'rtl') | null;
            format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | '';
            indent: number;
            version: number;
          };
          [k: string]: unknown;
        } | null;
        enableLink?: boolean | null;
        link?: {
          type?: ('reference' | 'custom') | null;
          newTab?: boolean | null;
          reference?:
            | ({
                relationTo: 'pages';
                value: number | Page;
              } | null)
            | ({
                relationTo: 'posts';
                value: number | Post;
              } | null);
          url?: string | null;
          label: string;
          /**
           * Choose how the link should be rendered.
           */
          appearance?: ('default' | 'outline') | null;
        };
        id?: string | null;
      }[]
    | null;
  id?: string | null;
  blockName?: string | null;
  blockType: 'content';
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "MediaBlock".
 */
export interface MediaBlock {
  media: number | Media;
  id?: string | null;
  blockName?: string | null;
  blockType: 'mediaBlock';
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "ArchiveBlock".
 */
export interface ArchiveBlock {
  introContent?: {
    root: {
      type: string;
      children: {
        type: string;
        version: number;
        [k: string]: unknown;
      }[];
      direction: ('ltr' | 'rtl') | null;
      format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | '';
      indent: number;
      version: number;
    };
    [k: string]: unknown;
  } | null;
  populateBy?: ('collection' | 'selection') | null;
  relationTo?: 'posts' | null;
  categories?: (number | Category)[] | null;
  limit?: number | null;
  selectedDocs?:
    | {
        relationTo: 'posts';
        value: number | Post;
      }[]
    | null;
  id?: string | null;
  blockName?: string | null;
  blockType: 'archive';
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "FormBlock".
 */
export interface FormBlock {
  form: number | Form;
  enableIntro?: boolean | null;
  introContent?: {
    root: {
      type: string;
      children: {
        type: string;
        version: number;
        [k: string]: unknown;
      }[];
      direction: ('ltr' | 'rtl') | null;
      format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | '';
      indent: number;
      version: number;
    };
    [k: string]: unknown;
  } | null;
  id?: string | null;
  blockName?: string | null;
  blockType: 'formBlock';
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "forms".
 */
export interface Form {
  id: number;
  /**
   * Enter a custom type for this form
   */
  type: string;
  title: string;
  fields?:
    | (
        | {
            name: string;
            label?: string | null;
            width?: number | null;
            required?: boolean | null;
            defaultValue?: boolean | null;
            id?: string | null;
            blockName?: string | null;
            blockType: 'checkbox';
          }
        | {
            name: string;
            label?: string | null;
            width?: number | null;
            required?: boolean | null;
            id?: string | null;
            blockName?: string | null;
            blockType: 'country';
          }
        | {
            name: string;
            label?: string | null;
            width?: number | null;
            required?: boolean | null;
            id?: string | null;
            blockName?: string | null;
            blockType: 'email';
          }
        | {
            message?: {
              root: {
                type: string;
                children: {
                  type: string;
                  version: number;
                  [k: string]: unknown;
                }[];
                direction: ('ltr' | 'rtl') | null;
                format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | '';
                indent: number;
                version: number;
              };
              [k: string]: unknown;
            } | null;
            id?: string | null;
            blockName?: string | null;
            blockType: 'message';
          }
        | {
            name: string;
            label?: string | null;
            width?: number | null;
            defaultValue?: number | null;
            required?: boolean | null;
            id?: string | null;
            blockName?: string | null;
            blockType: 'number';
          }
        | {
            name: string;
            label?: string | null;
            width?: number | null;
            defaultValue?: string | null;
            placeholder?: string | null;
            options?:
              | {
                  label: string;
                  value: string;
                  id?: string | null;
                }[]
              | null;
            required?: boolean | null;
            id?: string | null;
            blockName?: string | null;
            blockType: 'select';
          }
        | {
            name: string;
            label?: string | null;
            width?: number | null;
            required?: boolean | null;
            id?: string | null;
            blockName?: string | null;
            blockType: 'state';
          }
        | {
            name: string;
            label?: string | null;
            width?: number | null;
            defaultValue?: string | null;
            required?: boolean | null;
            id?: string | null;
            blockName?: string | null;
            blockType: 'text';
          }
        | {
            name: string;
            label?: string | null;
            width?: number | null;
            defaultValue?: string | null;
            required?: boolean | null;
            id?: string | null;
            blockName?: string | null;
            blockType: 'textarea';
          }
      )[]
    | null;
  submitButtonLabel?: string | null;
  /**
   * Choose whether to display an on-page message or redirect to a different page after they submit the form.
   */
  confirmationType?: ('message' | 'redirect') | null;
  confirmationMessage?: {
    root: {
      type: string;
      children: {
        type: string;
        version: number;
        [k: string]: unknown;
      }[];
      direction: ('ltr' | 'rtl') | null;
      format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | '';
      indent: number;
      version: number;
    };
    [k: string]: unknown;
  } | null;
  redirect?: {
    url: string;
  };
  /**
   * Send custom emails when the form submits. Use comma separated lists to send the same email to multiple recipients. To reference a value from this form, wrap that field's name with double curly brackets, i.e. {{firstName}}. You can use a wildcard {{*}} to output all data and {{*:table}} to format it as an HTML table in the email.
   */
  emails?:
    | {
        emailTo?: string | null;
        cc?: string | null;
        bcc?: string | null;
        replyTo?: string | null;
        emailFrom?: string | null;
        subject: string;
        /**
         * Enter the message that should be sent in this email.
         */
        message?: {
          root: {
            type: string;
            children: {
              type: string;
              version: number;
              [k: string]: unknown;
            }[];
            direction: ('ltr' | 'rtl') | null;
            format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | '';
            indent: number;
            version: number;
          };
          [k: string]: unknown;
        } | null;
        id?: string | null;
      }[]
    | null;
  status: 'active' | 'inactive';
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "CardsBlock".
 */
export interface CardsBlock {
  sections?:
    | {
        heading?: string | null;
        cards?:
          | {
              image?: (number | null) | Media;
              title?: string | null;
              description?: string | null;
              enableLink?: boolean | null;
              link?: {
                type?: ('reference' | 'custom') | null;
                newTab?: boolean | null;
                reference?:
                  | ({
                      relationTo: 'pages';
                      value: number | Page;
                    } | null)
                  | ({
                      relationTo: 'posts';
                      value: number | Post;
                    } | null);
                url?: string | null;
                label: string;
                /**
                 * Choose how the link should be rendered.
                 */
                appearance?: ('default' | 'outline') | null;
              };
              id?: string | null;
            }[]
          | null;
        id?: string | null;
      }[]
    | null;
  id?: string | null;
  blockName?: string | null;
  blockType: 'cardsBlock';
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "CardDetailBlock".
 */
export interface CardDetailBlock {
  banner?: (number | null) | Media;
  title?: string | null;
  category?: string | null;
  introContent?: {
    root: {
      type: string;
      children: {
        type: string;
        version: number;
        [k: string]: unknown;
      }[];
      direction: ('ltr' | 'rtl') | null;
      format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | '';
      indent: number;
      version: number;
    };
    [k: string]: unknown;
  } | null;
  id?: string | null;
  blockName?: string | null;
  blockType: 'cardDetailBlock';
}
/**
 * Stores membership-related information for users, including points, rank, and activity details.
 *
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "memberships".
 */
export interface Membership {
  id: number;
  user: number | User;
  totalPoints: number;
  membershipRank?: (number | null) | MembershipRankConfig;
  lastActive: string;
  pointsExpirationDate?: string | null;
  /**
   * Thời gian nhận điểm sinh nhật gần nhất
   */
  lastReceivedBirthdayPointAt?: string | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * Cấu hình các hạng của Membership
 *
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "membership-rank-configs".
 */
export interface MembershipRankConfig {
  id: number;
  rankName: 'Tier1' | 'Tier2' | 'Tier3' | 'Tier4';
  rankNameLabel?: string | null;
  /**
   * Khoảng thời gian nếu không hoạt động sẽ bị xuống hạng (ngày) (không tính hạng thấp nhất)
   */
  expiresIn?: number | null;
  description?: string | null;
  condition: {
    /**
     * Số điểm tối thiểu để đạt hạng này (1 điểm = 1000 VND doanh thu)
     */
    minPoints: number;
  };
  benefits?: {
    /**
     * Số điểm tặng vào dịp sinh nhật của thành viên thuộc hạng này
     */
    birthdayPoints?: number | null;
    /**
     * Loại vé tặng khi thành viên thăng hạng
     */
    ticketGift?: ('zone1' | 'zone2' | 'zone3' | 'zone4' | 'zone5') | null;
    /**
     * Thời gian hết hạn vé tặng (ngày)
     */
    giftExpiresIn?: number | null;
    /**
     * Phần trăm giảm giá khi mua vé cho hạng này (%)
     */
    discountPercentage?: number | null;
    /**
     * Cho phép check-in tại khu VIP khi tham gia show
     */
    vipCheckIn?: boolean | null;
  };
  updatedAt: string;
  createdAt: string;
}
/**
 * Quản lý các vé tặng thăng hạng của thành viên
 *
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "membership-gifts".
 */
export interface MembershipGift {
  id: number;
  user: number | User;
  giftType: 'giftTicket';
  /**
   * Loại vé tặng khi thành viên thăng hạng
   */
  ticketGift?: ('zone1' | 'zone2' | 'zone3' | 'zone4' | 'zone5') | null;
  receivedAt?: string | null;
  /**
   * Thời gian hết hạn của quà tặng(Nếu không thiết lập thì quà tặng sẽ không hết hạn)
   */
  expiresAt?: string | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "membership-histories".
 */
export interface MembershipHistory {
  id: number;
  /**
   * User người mà có thay đổi điểm tích lũy hoặc hạng thành viên
   */
  user: number | User;
  membership?: (number | null) | Membership;
  /**
   * Đơn hàng liên quan
   */
  order?: (number | null) | Order;
  /**
   * Số điểm của Membership trước khi thay đổi
   */
  pointsBefore?: number | null;
  /**
   * Số điểm thay đổi (dương hoặc âm, 0 nếu không thay đổi điểm)
   */
  pointsChange?: number | null;
  /**
   * Số điểm của Membership sau khi thay đổi
   */
  pointsAfter?: number | null;
  type: 'earned' | 'spent' | 'birthday' | 'receivedTicketGift';
  /**
   * Mô tả giao dịch, ví dụ: "Tích điểm từ đơn hàng #123"
   */
  description?: string | null;
  /**
   * Thông tin bổ sung về giao dịch
   */
  moreInformation?:
    | {
        [k: string]: unknown;
      }
    | unknown[]
    | string
    | number
    | boolean
    | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "orders".
 */
export interface Order {
  id: number;
  orderCode?: string | null;
  user?: (number | null) | User;
  category?: string | null;
  status?: ('processing' | 'canceled' | 'completed' | 'failed') | null;
  currency?: string | null;
  /**
   * Legacy field for a single promotion. Use "promotionsApplied" instead.
   */
  promotion?: (number | null) | Promotion;
  /**
   * Legacy field for a single promotion. Use "promotionsApplied" instead.
   */
  promotionCode?: string | null;
  /**
   * List of promotions applied to this order
   */
  promotionsApplied?:
    | {
        promotion: number | Promotion;
        promotionCode: string;
        discountAmount: number;
        id?: string | null;
      }[]
    | null;
  totalBeforeDiscount?: number | null;
  totalDiscount?: number | null;
  total?: number | null;
  customerData?:
    | {
        [k: string]: unknown;
      }
    | unknown[]
    | string
    | number
    | boolean
    | null;
  note?: string | null;
  /**
   * Affiliate information
   */
  affiliate?: {
    /**
     * Affiliate link used for this order
     */
    affiliateLink?: (number | null) | AffiliateLink;
    /**
     * Affiliate code used for this order
     */
    affiliateCode?: string | null;
    /**
     * Affiliate user who referred this order
     */
    affiliateUser?: (number | null) | User;
  };
  expireAt?: string | null;
  createdByAdmin?: (number | null) | Admin;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "promotions".
 */
export interface Promotion {
  id: number;
  code: string;
  event?: (number | null) | Event;
  appliedTicketClasses?:
    | {
        ticketClass: string;
        id?: string | null;
      }[]
    | null;
  maxRedemptions: number;
  totalUsed?: number | null;
  perUserLimit: number;
  conditions?: {
    isApplyCondition?: boolean | null;
    minTickets?: number | null;
  };
  discountApplyScope?: ('total_order_value' | 'per_order_item') | null;
  discountType: 'percentage' | 'fixed_amount';
  discountValue: number;
  startDate: string;
  endDate: string;
  status: 'draft' | 'active' | 'disabled';
  isPrivate?: boolean | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "events".
 */
export interface Event {
  id: number;
  title?: string | null;
  slug?: string | null;
  description?: string | null;
  detailDescription?: {
    root: {
      type: string;
      children: {
        type: string;
        version: number;
        [k: string]: unknown;
      }[];
      direction: ('ltr' | 'rtl') | null;
      format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | '';
      indent: number;
      version: number;
    };
    [k: string]: unknown;
  } | null;
  keyword?: string | null;
  startDatetime?: string | null;
  endDatetime?: string | null;
  schedules?:
    | {
        date?: string | null;
        scheduleImage?: (number | null) | Media;
        details?:
          | {
              time?: string | null;
              name?: string | null;
              description?: string | null;
              id?: string | null;
            }[]
          | null;
        id?: string | null;
      }[]
    | null;
  showAfterExpiration?: boolean | null;
  showTicketsAutomatically?: boolean | null;
  eventLocation?: string | null;
  eventTermsAndConditions?: string | null;
  ticketPrices?:
    | {
        name?: string | null;
        /**
         * Giá trị giảm dần theo khu vực, với Zone 1 là vé đắt nhất.
         */
        key?: ('zone1' | 'zone2' | 'zone3' | 'zone4' | 'zone5') | null;
        price?: number | null;
        currency?: string | null;
        quantity?: number | null;
        id?: string | null;
      }[]
    | null;
  vat?: {
    enabled?: boolean | null;
    percentage?: number | null;
    note?: string | null;
  };
  eventLogo?: (number | null) | Media;
  eventBanner?: (number | null) | Media;
  mobileEventBanner?: (number | null) | Media;
  eventThumbnail?: (number | null) | Media;
  sponsorLogo?: (number | null) | Media;
  ticketQuantityLimitation?: ('perTicketType' | 'perEvent') | null;
  configuration?: {
    showBannerTitle?: boolean | null;
    showBannerTime?: boolean | null;
    showBannerLocation?: boolean | null;
    showBannerDescription?: boolean | null;
  };
  seatingChart?: (number | null) | SeatingChart;
  status?: ('draft' | 'published_upcoming' | 'published_open_sales' | 'completed' | 'cancelled') | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "seatingCharts".
 */
export interface SeatingChart {
  id: number;
  title: string;
  seatMap?: (number | null) | Media;
  chartMapJson?:
    | {
        [k: string]: unknown;
      }
    | unknown[]
    | string
    | number
    | boolean
    | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "affiliate-links".
 */
export interface AffiliateLink {
  id: number;
  name?: string | null;
  affiliateUser: number | User;
  event?: (number | null) | Event;
  affiliateCode: string;
  /**
   * Promotion filtered by event
   */
  affiliatePromotion?: (number | null) | Promotion;
  promotionCode?: string | null;
  /**
   * UTM parameters (auto or manual)
   */
  utmParams?: {
    /**
     * UTM Source (Optional)
     */
    source?: string | null;
    /**
     * UTM Medium (Optional)
     */
    medium?: string | null;
    /**
     * UTM Campaign (Optional)
     */
    campaign?: string | null;
    /**
     * UTM Term (Optional)
     */
    term?: string | null;
    /**
     * UTM Content (Optional)
     */
    content?: string | null;
  };
  /**
   * Slug for the affiliate link
   */
  slug?: string | null;
  /**
   * Target link for the affiliate
   */
  targetLink?: string | null;
  status?: ('active' | 'disabled') | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "admins".
 */
export interface Admin {
  id: number;
  firstName: string;
  lastName: string;
  role: 'event-admin' | 'admin' | 'super-admin';
  lastActive?: string | null;
  updatedAt: string;
  createdAt: string;
  email: string;
  resetPasswordToken?: string | null;
  resetPasswordExpiration?: string | null;
  salt?: string | null;
  hash?: string | null;
  loginAttempts?: number | null;
  lockUntil?: string | null;
  sessions?:
    | {
        id: string;
        createdAt?: string | null;
        expiresAt: string;
      }[]
    | null;
  password?: string | null;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "checkinRecords".
 */
export interface CheckinRecord {
  id: number;
  event: number | Event;
  user: number | User;
  ticket: number | Ticket;
  seat: string;
  ticketCode: string;
  eventScheduleId?: string | null;
  eventDate?: string | null;
  checkInTime?: string | null;
  checkedInBy?: (number | null) | Admin;
  ticketGivenTime?: string | null;
  ticketGivenBy?: string | null;
  /**
   * True if this record was created from manual entry, false if via QR scan
   */
  manual: boolean;
  deletedAt?: string | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "tickets".
 */
export interface Ticket {
  id: number;
  attendeeName?: string | null;
  user?: (number | null) | User;
  userEmail?: string | null;
  ticketCode?: string | null;
  seat?: string | null;
  ticketPriceName?: string | null;
  ticketPriceInfo?:
    | {
        [k: string]: unknown;
      }
    | unknown[]
    | string
    | number
    | boolean
    | null;
  event?: (number | null) | Event;
  eventScheduleId?: string | null;
  eventDate?: string | null;
  orderItem?: (number | null) | OrderItem;
  order?: (number | null) | Order;
  orderCode?: string | null;
  status?: ('booked' | 'pending_payment' | 'hold' | 'cancelled') | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "orderItems".
 */
export interface OrderItem {
  id: number;
  order: number | Order;
  event: number | Event;
  ticketPriceId: string;
  ticketPriceName?: string | null;
  seat?: string | null;
  quantity: number;
  price: number;
  updatedAt: string;
  createdAt: string;
}
/**
 * Configure rules and conditions for promotions
 *
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "promotionConfigs".
 */
export interface PromotionConfig {
  id: number;
  name: string;
  description?: string | null;
  /**
   * If specified, this config will only apply to promotions for this event
   */
  event?: (number | null) | Event;
  validationRules?: {
    /**
     * Allow applying multiple promotions to the same order
     */
    allowApplyingMultiplePromotions?: boolean | null;
    maxAppliedPromotions?: number | null;
  };
  stackingRules?: {
    isStackable?: boolean | null;
  };
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "userPromotionRedemptions".
 */
export interface UserPromotionRedemption {
  id: number;
  promotion: number | Promotion;
  payment: number | Payment;
  event?: (number | null) | Event;
  user: number | User;
  redeemAt?: string | null;
  expireAt?: string | null;
  status: 'pending' | 'used' | 'cancelled';
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "payments".
 */
export interface Payment {
  id: number;
  user: number | User;
  order: number | Order;
  paymentMethod?: string | null;
  currency?: string | null;
  /**
   * Legacy field for a single promotion. Use "promotionsApplied" instead.
   */
  promotion?: (number | null) | Promotion;
  /**
   * Legacy field for a single promotion. Use "promotionsApplied" instead.
   */
  promotionCode?: string | null;
  /**
   * List of promotions applied to this order
   */
  promotionsApplied?:
    | {
        promotion: number | Promotion;
        promotionCode: string;
        discountAmount: number;
        id?: string | null;
      }[]
    | null;
  totalBeforeDiscount?: number | null;
  totalDiscount?: number | null;
  total: number;
  appTransId?: string | null;
  paymentData?:
    | {
        [k: string]: unknown;
      }
    | unknown[]
    | string
    | number
    | boolean
    | null;
  transaction?: {
    code?: string | null;
  };
  status: 'processing' | 'canceled' | 'paid' | 'failed';
  paidAt?: string | null;
  expireAt?: string | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * Manage ticket gift transfers between users
 *
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "ticket-gifts".
 */
export interface TicketGift {
  id: number;
  /**
   * Unique code for this gift transfer
   */
  giftCode: string;
  /**
   * User who is gifting the tickets
   */
  originalOwner: number | User;
  /**
   * Email address of the gift recipient
   */
  recipientEmail: string;
  recipientFirstName: string;
  recipientLastName: string;
  recipientPhone?: string | null;
  /**
   * User account created/linked when gift is accepted
   */
  recipientUser?: (number | null) | User;
  /**
   * Tickets being gifted
   */
  tickets: (number | Ticket)[];
  status: 'pending' | 'accepted' | 'rejected' | 'expired';
  /**
   * Optional message from the gift sender
   */
  message?: string | null;
  acceptedAt?: string | null;
  rejectedAt?: string | null;
  /**
   * Gift expires after this date (default: 7 days)
   */
  expiresAt: string;
  /**
   * Admin who processed this gift (if manually processed)
   */
  processedBy?: (number | null) | Admin;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "seatHoldings".
 */
export interface SeatHolding {
  id: number;
  seatName?: string | null;
  ticketClasses?:
    | {
        name?: string | null;
        quantity?: number | null;
        id?: string | null;
      }[]
    | null;
  event: number | Event;
  eventScheduleId?: string | null;
  code: string;
  userInfo?:
    | {
        [k: string]: unknown;
      }
    | unknown[]
    | string
    | number
    | boolean
    | null;
  closedAt?: string | null;
  expire_time?: string | null;
  ipAddress?: string | null;
  userAgent?: string | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "partners".
 */
export interface Partner {
  id: number;
  name: string;
  logo?: (number | null) | Media;
  description?: string | null;
  link?: string | null;
  status?: ('active' | 'inactive') | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "performers".
 */
export interface Performer {
  id: number;
  name?: string | null;
  displayOrder?: number | null;
  avatar?: (number | null) | Media;
  genre?: string | null;
  role?: string | null;
  description?: string | null;
  status?: ('active' | 'inactive') | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "activities".
 */
export interface Activity {
  id: number;
  mainTitle: string;
  description?: string | null;
  status?: ('active' | 'inactive') | null;
  list?:
    | {
        title?: string | null;
        description?: string | null;
        image?: (number | null) | Media;
        isShow?: boolean | null;
        id?: string | null;
      }[]
    | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "faqs".
 */
export interface Faq {
  id: number;
  question: string;
  answer: string;
  status?: ('active' | 'inactive') | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "emails".
 */
export interface Email {
  id: number;
  user?: (number | null) | User;
  event?: (number | null) | Event;
  ticket?: (number | null) | Ticket;
  to: string;
  from?: string | null;
  cc?: string | null;
  subject: string;
  html?: string | null;
  text?: string | null;
  provider?: string | null;
  extraData?:
    | {
        [k: string]: unknown;
      }
    | unknown[]
    | string
    | number
    | boolean
    | null;
  status?: ('pending' | 'sent' | 'failed') | null;
  sentAt?: string | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * Cấu hình các hạng của Affiliate Seller
 *
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "affiliate-ranks".
 */
export interface AffiliateRank {
  id: number;
  rankName: 'Tier1' | 'Tier2' | 'Tier3' | 'Tier4';
  rankNameLabel?: string | null;
  description?: string | null;
  /**
   * Số điểm tối thiểu để đạt hạng này (1 điểm = 1000 VND doanh thu)
   */
  minPoints: number;
  rewards?: {
    ticketRewards?:
      | {
          minTickets?: number | null;
          maxTickets?: number | null;
          minRevenue: number;
          maxRevenue?: number | null;
          rewardTickets: number;
          id?: string | null;
        }[]
      | null;
    commissionRewards?:
      | {
          minTickets?: number | null;
          maxTickets?: number | null;
          minRevenue: number;
          maxRevenue?: number | null;
          commissionRate: number;
          id?: string | null;
        }[]
      | null;
  };
  updatedAt: string;
  createdAt: string;
}
/**
 * Cấu hình hạng của Affiliate Seller trong từng event cụ thể
 *
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "event-affiliate-ranks".
 */
export interface EventAffiliateRank {
  id: number;
  /**
   * Sự kiện mà hạng này được áp dụng
   */
  event: number | Event;
  /**
   * Hạng được gán cho Affiliate User trong event
   */
  rankName: 'Tier1' | 'Tier2' | 'Tier3' | 'Tier4';
  rankNameLabel?: string | null;
  /**
   * Trạng thái của hạng trong event: Draft (bản nháp), Active (hoạt động), Disabled (vô hiệu hóa)
   */
  status: 'draft' | 'active' | 'disabled';
  eventRewards?: {
    ticketRewards?:
      | {
          minTickets?: number | null;
          maxTickets?: number | null;
          minRevenue: number;
          maxRevenue?: number | null;
          rewardTickets: number;
          id?: string | null;
        }[]
      | null;
    commissionRewards?:
      | {
          minTickets?: number | null;
          maxTickets?: number | null;
          minRevenue: number;
          maxRevenue?: number | null;
          commissionRate: number;
          id?: string | null;
        }[]
      | null;
  };
  updatedAt: string;
  createdAt: string;
}
/**
 * Lưu trữ thông tin hạng tổng của Affiliate User
 *
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "affiliate-user-ranks".
 */
export interface AffiliateUserRank {
  id: number;
  /**
   * Chọn Affiliate User (chỉ hiển thị users có role là affiliate)
   */
  affiliateUser: number | User;
  /**
   * Hạng hiện tại của Affiliate User
   */
  currentRank: 'Tier1' | 'Tier2' | 'Tier3' | 'Tier4';
  /**
   * Tổng số điểm tích lũy (1 điểm = 1000 VND doanh thu)
   */
  totalPoints: number;
  /**
   * Tổng doanh thu từ các đơn hàng của Affiliate User. Sẽ tính phần thưởng dựa trên giá trị này
   */
  totalRevenue: number;
  /**
   * Tổng tiền trước khi trừ thuế VAT của Affiliate User
   */
  totalRevenueBeforeTax: number;
  /**
   * Tổng tiền từ các đơn hàng của Affiliate User.
   */
  totalRevenueAfterTax: number;
  /**
   * Tổng tiền trước giảm giá từ các đơn hàng của Affiliate User
   */
  totalRevenueBeforeDiscount: number;
  /**
   * Tổng số vé đã bán được trong tất cả các sự kiện
   */
  totalTicketsSold: number;
  /**
   * Tổng số tiền hoa hồng đã nhận được từ các sự kiện
   */
  totalCommissionEarned: number;
  /**
   * Tổng số vé thưởng đã nhận được từ các sự kiện
   */
  totalTicketsRewarded: number;
  /**
   * Ngày Affiliate User đạt được hạng hiện tại
   */
  rankAchievedDate?: string | null;
  /**
   * Thời điểm Affiliate User thực hiện hành động gần nhất (bán vé, tích điểm, nâng hạng, v.v.)
   */
  lastActivityDate?: string | null;
  /**
   * Hạng mà Affiliate User đủ điều kiện nâng cấp nhưng chưa xác nhận
   */
  pendingRankUpgrade?: ('Tier1' | 'Tier2' | 'Tier3' | 'Tier4') | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * Quản lý hạng của Affiliate Seller trong từng event cụ thể
 *
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "event-affiliate-user-ranks".
 */
export interface EventAffiliateUserRank {
  id: number;
  /**
   * Sự kiện mà hạng này được áp dụng
   */
  event: number | Event;
  /**
   * Hạng được gán cho Affiliate Seller trong sự kiện này (ví dụ: Fan, Ambassador)
   */
  eventAffiliateRank: number | EventAffiliateRank;
  /**
   * Affiliate User
   */
  affiliateUser: number | User;
  /**
   * Trạng thái của hạng trong event: Draft (bản nháp), Active (hoạt động), Disabled (vô hiệu hóa)
   */
  status: 'draft' | 'active' | 'disabled';
  /**
   * Khi khóa, hạng này sẽ không thay đổi trong suốt event
   */
  isLocked?: boolean | null;
  /**
   * Tổng số điểm tích lũy (1 điểm = 1000 VND doanh thu) của Affiliate User trong event này
   */
  totalPoints: number;
  /**
   * Tổng doanh thu từ các đơn hàng của Affiliate User. Sẽ tính phần thưởng dựa trên giá trị này
   */
  totalRevenue: number;
  /**
   * Tổng tiền trước khi trừ thuế VAT của Affiliate User
   */
  totalRevenueBeforeTax: number;
  /**
   * Tổng tiền từ các đơn hàng của Affiliate User.
   */
  totalRevenueAfterTax: number;
  /**
   * Tổng tiền trước giảm giá từ các đơn hàng của Affiliate User
   */
  totalRevenueBeforeDiscount: number;
  /**
   * Tổng số vé đã bán được trong tất cả sự kiện
   */
  totalTicketsSold: number;
  /**
   * Tổng số tiền hoa hồng có thể nhận được từ sự kiện
   */
  totalCommissionEarned: number;
  /**
   * Tổng số vé thưởng có thể nhận được từ sự kiện
   */
  totalTicketsRewarded: number;
  /**
   * Thời điểm Affiliate User thực hiện hành động gần nhất (bán vé, tích điểm, v.v.) trong event này
   */
  lastActivityDate?: string | null;
  /**
   * Khi đã hoàn thành, những giá trị sẽ được tính toán và lưu vào các thông số hạng tổng của Affiliate User. Chỉ thực hiện hành động này sau khi event đã kết thúc
   */
  isCompleted?: boolean | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * Lưu trữ lịch sử các thay đổi điểm và hạng của Affiliate User
 *
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "affiliate-rank-logs".
 */
export interface AffiliateRankLog {
  id: number;
  /**
   * Affiliate User liên quan đến log này
   */
  affiliateUser: number | User;
  /**
   * Xác định log này liên quan đến hạng tổng hay hạng theo sự kiện
   */
  rankContext: 'user' | 'event';
  /**
   * Loại hành động liên quan đến điểm hoặc hạng
   */
  actionType:
    | 'add_points'
    | 'subtract_points'
    | 'rank_upgrade'
    | 'rank_downgrade'
    | 'confirm_rank_upgrade'
    | 'event_completed';
  /**
   * Số điểm thay đổi (dương hoặc âm, 0 nếu không thay đổi điểm)
   */
  pointsChange?: number | null;
  /**
   * Số điểm của Affiliate User trước khi sự kiện xảy ra
   */
  pointsBefore?: number | null;
  /**
   * Số điểm của Affiliate User sau khi sự kiện xảy ra
   */
  pointsAfter?: number | null;
  /**
   * Hạng trước khi sự kiện xảy ra (nếu có, áp dụng cho rank tổng)
   */
  rankBefore?: ('Tier1' | 'Tier2' | 'Tier3' | 'Tier4') | null;
  /**
   * Hạng sau khi sự kiện xảy ra (nếu có, áp dụng cho rank tổng)
   */
  rankAfter?: ('Tier1' | 'Tier2' | 'Tier3' | 'Tier4') | null;
  /**
   * Hạng trong event nếu log liên quan đến EventAffiliateUserRanks
   */
  eventAffiliateRank?: (number | null) | EventAffiliateRank;
  /**
   * Mô tả chi tiết về sự kiện (ví dụ: lý do thay đổi điểm hoặc hạng)
   */
  description?: string | null;
  /**
   * Thời gian xảy ra hành động.
   */
  occurredAt: string;
  /**
   * Sự kiện liên quan đến thay đổi này (nếu có)
   */
  event?: (number | null) | Event;
  /**
   * Đơn hàng liên quan đến thay đổi này (nếu có)
   */
  order?: (number | null) | Order;
  /**
   * Admin thực hiện hành động này (nếu có)
   */
  adminUser?: (number | null) | Admin;
  updatedAt: string;
  createdAt: string;
}
/**
 * Configure tier-based affiliate reward systems with commission percentages and free ticket rewards
 *
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "affiliate-settings".
 */
export interface AffiliateSetting {
  id: number;
  /**
   * A descriptive name for this affiliate tier configuration (e.g. "Concert XYZ Affiliate Program")
   */
  name: string;
  /**
   * The event this affiliate setting applies to
   */
  event: number | Event;
  /**
   * The affiliate user setting applies to
   */
  affiliateUser: number | User;
  /**
   * Promotions that are valid for this affiliate user
   */
  promotions?:
    | {
        /**
         * Promotions filtered by event
         */
        promotion: number | Promotion;
        id?: string | null;
      }[]
    | null;
  /**
   * Whether this affiliate setting is currently active
   */
  isActive?: boolean | null;
  /**
   * Optional description of this affiliate program
   */
  description?: string | null;
  /**
   * Define the tier system with different reward levels based on performance
   */
  tiers: {
    /**
     * Name for this tier (e.g. "Beginner", "Intermediate", "Professional")
     */
    tierName: string;
    /**
     * Numeric level for this tier (1 = lowest, higher numbers = higher tiers)
     */
    tierLevel: number;
    /**
     * Define how affiliates qualify for this tier
     */
    quaCriteria: {
      /**
       * Minimum number of tickets that must be sold to reach this tier
       */
      minTicketsSold: number;
      /**
       * Maximum number of tickets for this tier (leave empty for unlimited)
       */
      maxTicketsSold?: number | null;
      /**
       * Alternative qualification: minimum net revenue in VND
       */
      minNetRevenue?: number | null;
      /**
       * Maximum net revenue for this tier (leave empty for unlimited)
       */
      maxNetRevenue?: number | null;
      /**
       * Which ticket types count towards tier qualification (leave empty for all types)
       */
      eligTicketTypes?: ('zone1' | 'zone2' | 'zone3' | 'zone4' | 'zone5')[] | null;
    };
    /**
     * Define the rewards for reaching this tier
     */
    rewards: {
      /**
       * Percentage of net revenue to pay as commission (e.g. 5 for 5%)
       */
      commissionPercentage: number;
      /**
       * Free tickets awarded when reaching this tier
       */
      freeTickets?:
        | {
            /**
             * Class of free ticket to award. The value decreases by zone, with Zone 1 being the most expensive ticket.
             */
            ticketClass: 'zone1' | 'zone2' | 'zone3' | 'zone4' | 'zone5';
            /**
             * Number of free tickets to award
             */
            quantity: number;
            /**
             * Amount (in VND) to be used as a substitute if free tickets are not available.
             */
            ticketValue?: number | null;
            id?: string | null;
          }[]
        | null;
    };
    id?: string | null;
  }[];
  updatedAt: string;
  createdAt: string;
}
/**
 * Track affiliate link clicks and user interactions
 *
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "affiliate-click-logs".
 */
export interface AffiliateClickLog {
  id: number;
  affiliateUser: number | User;
  affiliateLink: number | AffiliateLink;
  /**
   * Unique session identifier to prevent duplicate tracking
   */
  sessionId?: string | null;
  /**
   * Client IP address
   */
  ip?: string | null;
  /**
   * Geographic location (if available)
   */
  location?: string | null;
  /**
   * HTTP referrer header
   */
  referrer?: string | null;
  /**
   * Browser user agent string
   */
  userAgent?: string | null;
  /**
   * Additional tracking data including device info, promo codes, etc.
   */
  moreInformation?:
    | {
        [k: string]: unknown;
      }
    | unknown[]
    | string
    | number
    | boolean
    | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "marketingTrackings".
 */
export interface MarketingTracking {
  id: number;
  order?: (number | null) | Order;
  description?: string | null;
  utmSource?: string | null;
  utmMedium?: string | null;
  utmCampaign?: string | null;
  utmTerm?: string | null;
  utmContent?: string | null;
  conversionType?: string | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "logs".
 */
export interface Log {
  id: number;
  action: string;
  description?: string | null;
  timestamp: string;
  status?: ('success' | 'error' | 'warning' | 'info') | null;
  /**
   * Additional data related to this log entry
   */
  data?:
    | {
        [k: string]: unknown;
      }
    | unknown[]
    | string
    | number
    | boolean
    | null;
  /**
   * Related order (if applicable)
   */
  order?: (number | null) | Order;
  /**
   * Related payment (if applicable)
   */
  payment?: (number | null) | Payment;
  /**
   * IP address where the action originated
   */
  ipAddress?: string | null;
  userAgent?: string | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "redirects".
 */
export interface Redirect {
  id: number;
  /**
   * You will need to rebuild the website when changing this field.
   */
  from: string;
  to?: {
    type?: ('reference' | 'custom') | null;
    reference?:
      | ({
          relationTo: 'pages';
          value: number | Page;
        } | null)
      | ({
          relationTo: 'posts';
          value: number | Post;
        } | null);
    url?: string | null;
  };
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "form-submissions".
 */
export interface FormSubmission {
  id: number;
  form: number | Form;
  submissionData?:
    | {
        field: string;
        value: string;
        id?: string | null;
      }[]
    | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This is a collection of automatically created search results. These results are used by the global site search and will be updated automatically as documents in the CMS are created or updated.
 *
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "search".
 */
export interface Search {
  id: number;
  title?: string | null;
  priority?: number | null;
  doc: {
    relationTo: 'posts';
    value: number | Post;
  };
  slug?: string | null;
  meta?: {
    title?: string | null;
    description?: string | null;
    image?: (number | null) | Media;
  };
  categories?:
    | {
        relationTo?: string | null;
        id?: string | null;
        title?: string | null;
      }[]
    | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "exports".
 */
export interface Export {
  id: number;
  name?: string | null;
  format: 'csv' | 'json';
  limit?: number | null;
  sort?: string | null;
  locale?: ('all' | 'en' | 'vi') | null;
  drafts?: ('yes' | 'no') | null;
  selectionToUse?: ('currentSelection' | 'currentFilters' | 'all') | null;
  fields?: string[] | null;
  collectionSlug: string;
  where?:
    | {
        [k: string]: unknown;
      }
    | unknown[]
    | string
    | number
    | boolean
    | null;
  updatedAt: string;
  createdAt: string;
  url?: string | null;
  thumbnailURL?: string | null;
  filename?: string | null;
  mimeType?: string | null;
  filesize?: number | null;
  width?: number | null;
  height?: number | null;
  focalX?: number | null;
  focalY?: number | null;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "payload-jobs".
 */
export interface PayloadJob {
  id: number;
  /**
   * Input data provided to the job
   */
  input?:
    | {
        [k: string]: unknown;
      }
    | unknown[]
    | string
    | number
    | boolean
    | null;
  taskStatus?:
    | {
        [k: string]: unknown;
      }
    | unknown[]
    | string
    | number
    | boolean
    | null;
  completedAt?: string | null;
  totalTried?: number | null;
  /**
   * If hasError is true this job will not be retried
   */
  hasError?: boolean | null;
  /**
   * If hasError is true, this is the error that caused it
   */
  error?:
    | {
        [k: string]: unknown;
      }
    | unknown[]
    | string
    | number
    | boolean
    | null;
  /**
   * Task execution log
   */
  log?:
    | {
        executedAt: string;
        completedAt: string;
        taskSlug: 'inline' | 'createCollectionExport' | 'schedulePublish';
        taskID: string;
        input?:
          | {
              [k: string]: unknown;
            }
          | unknown[]
          | string
          | number
          | boolean
          | null;
        output?:
          | {
              [k: string]: unknown;
            }
          | unknown[]
          | string
          | number
          | boolean
          | null;
        state: 'failed' | 'succeeded';
        error?:
          | {
              [k: string]: unknown;
            }
          | unknown[]
          | string
          | number
          | boolean
          | null;
        id?: string | null;
      }[]
    | null;
  taskSlug?: ('inline' | 'createCollectionExport' | 'schedulePublish') | null;
  queue?: string | null;
  waitUntil?: string | null;
  processing?: boolean | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "payload-locked-documents".
 */
export interface PayloadLockedDocument {
  id: number;
  document?:
    | ({
        relationTo: 'pages';
        value: number | Page;
      } | null)
    | ({
        relationTo: 'posts';
        value: number | Post;
      } | null)
    | ({
        relationTo: 'media';
        value: number | Media;
      } | null)
    | ({
        relationTo: 'categories';
        value: number | Category;
      } | null)
    | ({
        relationTo: 'users';
        value: number | User;
      } | null)
    | ({
        relationTo: 'memberships';
        value: number | Membership;
      } | null)
    | ({
        relationTo: 'membership-rank-configs';
        value: number | MembershipRankConfig;
      } | null)
    | ({
        relationTo: 'membership-gifts';
        value: number | MembershipGift;
      } | null)
    | ({
        relationTo: 'membership-histories';
        value: number | MembershipHistory;
      } | null)
    | ({
        relationTo: 'checkinRecords';
        value: number | CheckinRecord;
      } | null)
    | ({
        relationTo: 'seatingCharts';
        value: number | SeatingChart;
      } | null)
    | ({
        relationTo: 'events';
        value: number | Event;
      } | null)
    | ({
        relationTo: 'promotions';
        value: number | Promotion;
      } | null)
    | ({
        relationTo: 'promotionConfigs';
        value: number | PromotionConfig;
      } | null)
    | ({
        relationTo: 'userPromotionRedemptions';
        value: number | UserPromotionRedemption;
      } | null)
    | ({
        relationTo: 'orders';
        value: number | Order;
      } | null)
    | ({
        relationTo: 'orderItems';
        value: number | OrderItem;
      } | null)
    | ({
        relationTo: 'payments';
        value: number | Payment;
      } | null)
    | ({
        relationTo: 'tickets';
        value: number | Ticket;
      } | null)
    | ({
        relationTo: 'ticket-gifts';
        value: number | TicketGift;
      } | null)
    | ({
        relationTo: 'seatHoldings';
        value: number | SeatHolding;
      } | null)
    | ({
        relationTo: 'partners';
        value: number | Partner;
      } | null)
    | ({
        relationTo: 'performers';
        value: number | Performer;
      } | null)
    | ({
        relationTo: 'activities';
        value: number | Activity;
      } | null)
    | ({
        relationTo: 'faqs';
        value: number | Faq;
      } | null)
    | ({
        relationTo: 'admins';
        value: number | Admin;
      } | null)
    | ({
        relationTo: 'emails';
        value: number | Email;
      } | null)
    | ({
        relationTo: 'affiliate-ranks';
        value: number | AffiliateRank;
      } | null)
    | ({
        relationTo: 'event-affiliate-ranks';
        value: number | EventAffiliateRank;
      } | null)
    | ({
        relationTo: 'affiliate-user-ranks';
        value: number | AffiliateUserRank;
      } | null)
    | ({
        relationTo: 'event-affiliate-user-ranks';
        value: number | EventAffiliateUserRank;
      } | null)
    | ({
        relationTo: 'affiliate-rank-logs';
        value: number | AffiliateRankLog;
      } | null)
    | ({
        relationTo: 'affiliate-links';
        value: number | AffiliateLink;
      } | null)
    | ({
        relationTo: 'affiliate-settings';
        value: number | AffiliateSetting;
      } | null)
    | ({
        relationTo: 'affiliate-click-logs';
        value: number | AffiliateClickLog;
      } | null)
    | ({
        relationTo: 'marketingTrackings';
        value: number | MarketingTracking;
      } | null)
    | ({
        relationTo: 'logs';
        value: number | Log;
      } | null)
    | ({
        relationTo: 'redirects';
        value: number | Redirect;
      } | null)
    | ({
        relationTo: 'forms';
        value: number | Form;
      } | null)
    | ({
        relationTo: 'form-submissions';
        value: number | FormSubmission;
      } | null)
    | ({
        relationTo: 'search';
        value: number | Search;
      } | null)
    | ({
        relationTo: 'exports';
        value: number | Export;
      } | null)
    | ({
        relationTo: 'payload-jobs';
        value: number | PayloadJob;
      } | null);
  globalSlug?: string | null;
  user: {
    relationTo: 'admins';
    value: number | Admin;
  };
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "payload-preferences".
 */
export interface PayloadPreference {
  id: number;
  user: {
    relationTo: 'admins';
    value: number | Admin;
  };
  key?: string | null;
  value?:
    | {
        [k: string]: unknown;
      }
    | unknown[]
    | string
    | number
    | boolean
    | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "payload-migrations".
 */
export interface PayloadMigration {
  id: number;
  name?: string | null;
  batch?: number | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "pages_select".
 */
export interface PagesSelect<T extends boolean = true> {
  banner?: T;
  title?: T;
  description?: T;
  hero?:
    | T
    | {
        type?: T;
        richText?: T;
        links?:
          | T
          | {
              link?:
                | T
                | {
                    type?: T;
                    newTab?: T;
                    reference?: T;
                    url?: T;
                    label?: T;
                    appearance?: T;
                  };
              id?: T;
            };
        media?: T;
      };
  layout?:
    | T
    | {
        cta?: T | CallToActionBlockSelect<T>;
        content?: T | ContentBlockSelect<T>;
        mediaBlock?: T | MediaBlockSelect<T>;
        archive?: T | ArchiveBlockSelect<T>;
        formBlock?: T | FormBlockSelect<T>;
        cardsBlock?: T | CardsBlockSelect<T>;
        cardDetailBlock?: T | CardDetailBlockSelect<T>;
      };
  meta?:
    | T
    | {
        title?: T;
        image?: T;
        description?: T;
      };
  publishedAt?: T;
  slug?: T;
  slugLock?: T;
  parent?: T;
  breadcrumbs?:
    | T
    | {
        doc?: T;
        url?: T;
        label?: T;
        id?: T;
      };
  updatedAt?: T;
  createdAt?: T;
  _status?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "CallToActionBlock_select".
 */
export interface CallToActionBlockSelect<T extends boolean = true> {
  richText?: T;
  links?:
    | T
    | {
        link?:
          | T
          | {
              type?: T;
              newTab?: T;
              reference?: T;
              url?: T;
              label?: T;
              appearance?: T;
            };
        id?: T;
      };
  id?: T;
  blockName?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "ContentBlock_select".
 */
export interface ContentBlockSelect<T extends boolean = true> {
  columns?:
    | T
    | {
        size?: T;
        richText?: T;
        enableLink?: T;
        link?:
          | T
          | {
              type?: T;
              newTab?: T;
              reference?: T;
              url?: T;
              label?: T;
              appearance?: T;
            };
        id?: T;
      };
  id?: T;
  blockName?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "MediaBlock_select".
 */
export interface MediaBlockSelect<T extends boolean = true> {
  media?: T;
  id?: T;
  blockName?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "ArchiveBlock_select".
 */
export interface ArchiveBlockSelect<T extends boolean = true> {
  introContent?: T;
  populateBy?: T;
  relationTo?: T;
  categories?: T;
  limit?: T;
  selectedDocs?: T;
  id?: T;
  blockName?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "FormBlock_select".
 */
export interface FormBlockSelect<T extends boolean = true> {
  form?: T;
  enableIntro?: T;
  introContent?: T;
  id?: T;
  blockName?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "CardsBlock_select".
 */
export interface CardsBlockSelect<T extends boolean = true> {
  sections?:
    | T
    | {
        heading?: T;
        cards?:
          | T
          | {
              image?: T;
              title?: T;
              description?: T;
              enableLink?: T;
              link?:
                | T
                | {
                    type?: T;
                    newTab?: T;
                    reference?: T;
                    url?: T;
                    label?: T;
                    appearance?: T;
                  };
              id?: T;
            };
        id?: T;
      };
  id?: T;
  blockName?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "CardDetailBlock_select".
 */
export interface CardDetailBlockSelect<T extends boolean = true> {
  banner?: T;
  title?: T;
  category?: T;
  introContent?: T;
  id?: T;
  blockName?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "posts_select".
 */
export interface PostsSelect<T extends boolean = true> {
  title?: T;
  heroImage?: T;
  content?: T;
  relatedPosts?: T;
  categories?: T;
  meta?:
    | T
    | {
        title?: T;
        image?: T;
        description?: T;
      };
  publishedAt?: T;
  authors?: T;
  populatedAuthors?:
    | T
    | {
        id?: T;
        name?: T;
      };
  slug?: T;
  slugLock?: T;
  updatedAt?: T;
  createdAt?: T;
  _status?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "media_select".
 */
export interface MediaSelect<T extends boolean = true> {
  alt?: T;
  caption?: T;
  updatedAt?: T;
  createdAt?: T;
  url?: T;
  thumbnailURL?: T;
  filename?: T;
  mimeType?: T;
  filesize?: T;
  width?: T;
  height?: T;
  focalX?: T;
  focalY?: T;
  sizes?:
    | T
    | {
        thumbnail?:
          | T
          | {
              url?: T;
              width?: T;
              height?: T;
              mimeType?: T;
              filesize?: T;
              filename?: T;
            };
        square?:
          | T
          | {
              url?: T;
              width?: T;
              height?: T;
              mimeType?: T;
              filesize?: T;
              filename?: T;
            };
        small?:
          | T
          | {
              url?: T;
              width?: T;
              height?: T;
              mimeType?: T;
              filesize?: T;
              filename?: T;
            };
        medium?:
          | T
          | {
              url?: T;
              width?: T;
              height?: T;
              mimeType?: T;
              filesize?: T;
              filename?: T;
            };
        large?:
          | T
          | {
              url?: T;
              width?: T;
              height?: T;
              mimeType?: T;
              filesize?: T;
              filename?: T;
            };
        xlarge?:
          | T
          | {
              url?: T;
              width?: T;
              height?: T;
              mimeType?: T;
              filesize?: T;
              filename?: T;
            };
        og?:
          | T
          | {
              url?: T;
              width?: T;
              height?: T;
              mimeType?: T;
              filesize?: T;
              filename?: T;
            };
      };
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "categories_select".
 */
export interface CategoriesSelect<T extends boolean = true> {
  title?: T;
  slug?: T;
  slugLock?: T;
  parent?: T;
  breadcrumbs?:
    | T
    | {
        doc?: T;
        url?: T;
        label?: T;
        id?: T;
      };
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "users_select".
 */
export interface UsersSelect<T extends boolean = true> {
  email?: T;
  salt?: T;
  hash?: T;
  resetPasswordToken?: T;
  resetPasswordExpiration?: T;
  phoneNumber?: T;
  phoneNumbers?:
    | T
    | {
        phone?: T;
        createdAt?: T;
        isUsing?: T;
        id?: T;
      };
  username?: T;
  firstName?: T;
  lastName?: T;
  lastActive?: T;
  role?: T;
  affiliateStatus?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "memberships_select".
 */
export interface MembershipsSelect<T extends boolean = true> {
  user?: T;
  totalPoints?: T;
  membershipRank?: T;
  lastActive?: T;
  pointsExpirationDate?: T;
  lastReceivedBirthdayPointAt?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "membership-rank-configs_select".
 */
export interface MembershipRankConfigsSelect<T extends boolean = true> {
  rankName?: T;
  rankNameLabel?: T;
  expiresIn?: T;
  description?: T;
  condition?:
    | T
    | {
        minPoints?: T;
      };
  benefits?:
    | T
    | {
        birthdayPoints?: T;
        ticketGift?: T;
        giftExpiresIn?: T;
        discountPercentage?: T;
        vipCheckIn?: T;
      };
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "membership-gifts_select".
 */
export interface MembershipGiftsSelect<T extends boolean = true> {
  user?: T;
  giftType?: T;
  ticketGift?: T;
  receivedAt?: T;
  expiresAt?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "membership-histories_select".
 */
export interface MembershipHistoriesSelect<T extends boolean = true> {
  user?: T;
  membership?: T;
  order?: T;
  pointsBefore?: T;
  pointsChange?: T;
  pointsAfter?: T;
  type?: T;
  description?: T;
  moreInformation?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "checkinRecords_select".
 */
export interface CheckinRecordsSelect<T extends boolean = true> {
  event?: T;
  user?: T;
  ticket?: T;
  seat?: T;
  ticketCode?: T;
  eventScheduleId?: T;
  eventDate?: T;
  checkInTime?: T;
  checkedInBy?: T;
  ticketGivenTime?: T;
  ticketGivenBy?: T;
  manual?: T;
  deletedAt?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "seatingCharts_select".
 */
export interface SeatingChartsSelect<T extends boolean = true> {
  title?: T;
  seatMap?: T;
  chartMapJson?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "events_select".
 */
export interface EventsSelect<T extends boolean = true> {
  title?: T;
  slug?: T;
  description?: T;
  detailDescription?: T;
  keyword?: T;
  startDatetime?: T;
  endDatetime?: T;
  schedules?:
    | T
    | {
        date?: T;
        scheduleImage?: T;
        details?:
          | T
          | {
              time?: T;
              name?: T;
              description?: T;
              id?: T;
            };
        id?: T;
      };
  showAfterExpiration?: T;
  showTicketsAutomatically?: T;
  eventLocation?: T;
  eventTermsAndConditions?: T;
  ticketPrices?:
    | T
    | {
        name?: T;
        key?: T;
        price?: T;
        currency?: T;
        quantity?: T;
        id?: T;
      };
  vat?:
    | T
    | {
        enabled?: T;
        percentage?: T;
        note?: T;
      };
  eventLogo?: T;
  eventBanner?: T;
  mobileEventBanner?: T;
  eventThumbnail?: T;
  sponsorLogo?: T;
  ticketQuantityLimitation?: T;
  configuration?:
    | T
    | {
        showBannerTitle?: T;
        showBannerTime?: T;
        showBannerLocation?: T;
        showBannerDescription?: T;
      };
  seatingChart?: T;
  status?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "promotions_select".
 */
export interface PromotionsSelect<T extends boolean = true> {
  code?: T;
  event?: T;
  appliedTicketClasses?:
    | T
    | {
        ticketClass?: T;
        id?: T;
      };
  maxRedemptions?: T;
  totalUsed?: T;
  perUserLimit?: T;
  conditions?:
    | T
    | {
        isApplyCondition?: T;
        minTickets?: T;
      };
  discountApplyScope?: T;
  discountType?: T;
  discountValue?: T;
  startDate?: T;
  endDate?: T;
  status?: T;
  isPrivate?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "promotionConfigs_select".
 */
export interface PromotionConfigsSelect<T extends boolean = true> {
  name?: T;
  description?: T;
  event?: T;
  validationRules?:
    | T
    | {
        allowApplyingMultiplePromotions?: T;
        maxAppliedPromotions?: T;
      };
  stackingRules?:
    | T
    | {
        isStackable?: T;
      };
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "userPromotionRedemptions_select".
 */
export interface UserPromotionRedemptionsSelect<T extends boolean = true> {
  promotion?: T;
  payment?: T;
  event?: T;
  user?: T;
  redeemAt?: T;
  expireAt?: T;
  status?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "orders_select".
 */
export interface OrdersSelect<T extends boolean = true> {
  orderCode?: T;
  user?: T;
  category?: T;
  status?: T;
  currency?: T;
  promotion?: T;
  promotionCode?: T;
  promotionsApplied?:
    | T
    | {
        promotion?: T;
        promotionCode?: T;
        discountAmount?: T;
        id?: T;
      };
  totalBeforeDiscount?: T;
  totalDiscount?: T;
  total?: T;
  customerData?: T;
  note?: T;
  affiliate?:
    | T
    | {
        affiliateLink?: T;
        affiliateCode?: T;
        affiliateUser?: T;
      };
  expireAt?: T;
  createdByAdmin?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "orderItems_select".
 */
export interface OrderItemsSelect<T extends boolean = true> {
  order?: T;
  event?: T;
  ticketPriceId?: T;
  ticketPriceName?: T;
  seat?: T;
  quantity?: T;
  price?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "payments_select".
 */
export interface PaymentsSelect<T extends boolean = true> {
  user?: T;
  order?: T;
  paymentMethod?: T;
  currency?: T;
  promotion?: T;
  promotionCode?: T;
  promotionsApplied?:
    | T
    | {
        promotion?: T;
        promotionCode?: T;
        discountAmount?: T;
        id?: T;
      };
  totalBeforeDiscount?: T;
  totalDiscount?: T;
  total?: T;
  appTransId?: T;
  paymentData?: T;
  transaction?:
    | T
    | {
        code?: T;
      };
  status?: T;
  paidAt?: T;
  expireAt?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "tickets_select".
 */
export interface TicketsSelect<T extends boolean = true> {
  attendeeName?: T;
  user?: T;
  userEmail?: T;
  ticketCode?: T;
  seat?: T;
  ticketPriceName?: T;
  ticketPriceInfo?: T;
  event?: T;
  eventScheduleId?: T;
  eventDate?: T;
  orderItem?: T;
  order?: T;
  orderCode?: T;
  status?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "ticket-gifts_select".
 */
export interface TicketGiftsSelect<T extends boolean = true> {
  giftCode?: T;
  originalOwner?: T;
  recipientEmail?: T;
  recipientFirstName?: T;
  recipientLastName?: T;
  recipientPhone?: T;
  recipientUser?: T;
  tickets?: T;
  status?: T;
  message?: T;
  acceptedAt?: T;
  rejectedAt?: T;
  expiresAt?: T;
  processedBy?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "seatHoldings_select".
 */
export interface SeatHoldingsSelect<T extends boolean = true> {
  seatName?: T;
  ticketClasses?:
    | T
    | {
        name?: T;
        quantity?: T;
        id?: T;
      };
  event?: T;
  eventScheduleId?: T;
  code?: T;
  userInfo?: T;
  closedAt?: T;
  expire_time?: T;
  ipAddress?: T;
  userAgent?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "partners_select".
 */
export interface PartnersSelect<T extends boolean = true> {
  name?: T;
  logo?: T;
  description?: T;
  link?: T;
  status?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "performers_select".
 */
export interface PerformersSelect<T extends boolean = true> {
  name?: T;
  displayOrder?: T;
  avatar?: T;
  genre?: T;
  role?: T;
  description?: T;
  status?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "activities_select".
 */
export interface ActivitiesSelect<T extends boolean = true> {
  mainTitle?: T;
  description?: T;
  status?: T;
  list?:
    | T
    | {
        title?: T;
        description?: T;
        image?: T;
        isShow?: T;
        id?: T;
      };
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "faqs_select".
 */
export interface FaqsSelect<T extends boolean = true> {
  question?: T;
  answer?: T;
  status?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "admins_select".
 */
export interface AdminsSelect<T extends boolean = true> {
  firstName?: T;
  lastName?: T;
  role?: T;
  lastActive?: T;
  updatedAt?: T;
  createdAt?: T;
  email?: T;
  resetPasswordToken?: T;
  resetPasswordExpiration?: T;
  salt?: T;
  hash?: T;
  loginAttempts?: T;
  lockUntil?: T;
  sessions?:
    | T
    | {
        id?: T;
        createdAt?: T;
        expiresAt?: T;
      };
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "emails_select".
 */
export interface EmailsSelect<T extends boolean = true> {
  user?: T;
  event?: T;
  ticket?: T;
  to?: T;
  from?: T;
  cc?: T;
  subject?: T;
  html?: T;
  text?: T;
  provider?: T;
  extraData?: T;
  status?: T;
  sentAt?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "affiliate-ranks_select".
 */
export interface AffiliateRanksSelect<T extends boolean = true> {
  rankName?: T;
  rankNameLabel?: T;
  description?: T;
  minPoints?: T;
  rewards?:
    | T
    | {
        ticketRewards?:
          | T
          | {
              minTickets?: T;
              maxTickets?: T;
              minRevenue?: T;
              maxRevenue?: T;
              rewardTickets?: T;
              id?: T;
            };
        commissionRewards?:
          | T
          | {
              minTickets?: T;
              maxTickets?: T;
              minRevenue?: T;
              maxRevenue?: T;
              commissionRate?: T;
              id?: T;
            };
      };
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "event-affiliate-ranks_select".
 */
export interface EventAffiliateRanksSelect<T extends boolean = true> {
  event?: T;
  rankName?: T;
  rankNameLabel?: T;
  status?: T;
  eventRewards?:
    | T
    | {
        ticketRewards?:
          | T
          | {
              minTickets?: T;
              maxTickets?: T;
              minRevenue?: T;
              maxRevenue?: T;
              rewardTickets?: T;
              id?: T;
            };
        commissionRewards?:
          | T
          | {
              minTickets?: T;
              maxTickets?: T;
              minRevenue?: T;
              maxRevenue?: T;
              commissionRate?: T;
              id?: T;
            };
      };
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "affiliate-user-ranks_select".
 */
export interface AffiliateUserRanksSelect<T extends boolean = true> {
  affiliateUser?: T;
  currentRank?: T;
  totalPoints?: T;
  totalRevenue?: T;
  totalRevenueBeforeTax?: T;
  totalRevenueAfterTax?: T;
  totalRevenueBeforeDiscount?: T;
  totalTicketsSold?: T;
  totalCommissionEarned?: T;
  totalTicketsRewarded?: T;
  rankAchievedDate?: T;
  lastActivityDate?: T;
  pendingRankUpgrade?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "event-affiliate-user-ranks_select".
 */
export interface EventAffiliateUserRanksSelect<T extends boolean = true> {
  event?: T;
  eventAffiliateRank?: T;
  affiliateUser?: T;
  status?: T;
  isLocked?: T;
  totalPoints?: T;
  totalRevenue?: T;
  totalRevenueBeforeTax?: T;
  totalRevenueAfterTax?: T;
  totalRevenueBeforeDiscount?: T;
  totalTicketsSold?: T;
  totalCommissionEarned?: T;
  totalTicketsRewarded?: T;
  lastActivityDate?: T;
  isCompleted?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "affiliate-rank-logs_select".
 */
export interface AffiliateRankLogsSelect<T extends boolean = true> {
  affiliateUser?: T;
  rankContext?: T;
  actionType?: T;
  pointsChange?: T;
  pointsBefore?: T;
  pointsAfter?: T;
  rankBefore?: T;
  rankAfter?: T;
  eventAffiliateRank?: T;
  description?: T;
  occurredAt?: T;
  event?: T;
  order?: T;
  adminUser?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "affiliate-links_select".
 */
export interface AffiliateLinksSelect<T extends boolean = true> {
  name?: T;
  affiliateUser?: T;
  event?: T;
  affiliateCode?: T;
  affiliatePromotion?: T;
  promotionCode?: T;
  utmParams?:
    | T
    | {
        source?: T;
        medium?: T;
        campaign?: T;
        term?: T;
        content?: T;
      };
  slug?: T;
  targetLink?: T;
  status?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "affiliate-settings_select".
 */
export interface AffiliateSettingsSelect<T extends boolean = true> {
  name?: T;
  event?: T;
  affiliateUser?: T;
  promotions?:
    | T
    | {
        promotion?: T;
        id?: T;
      };
  isActive?: T;
  description?: T;
  tiers?:
    | T
    | {
        tierName?: T;
        tierLevel?: T;
        quaCriteria?:
          | T
          | {
              minTicketsSold?: T;
              maxTicketsSold?: T;
              minNetRevenue?: T;
              maxNetRevenue?: T;
              eligTicketTypes?: T;
            };
        rewards?:
          | T
          | {
              commissionPercentage?: T;
              freeTickets?:
                | T
                | {
                    ticketClass?: T;
                    quantity?: T;
                    ticketValue?: T;
                    id?: T;
                  };
            };
        id?: T;
      };
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "affiliate-click-logs_select".
 */
export interface AffiliateClickLogsSelect<T extends boolean = true> {
  affiliateUser?: T;
  affiliateLink?: T;
  sessionId?: T;
  ip?: T;
  location?: T;
  referrer?: T;
  userAgent?: T;
  moreInformation?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "marketingTrackings_select".
 */
export interface MarketingTrackingsSelect<T extends boolean = true> {
  order?: T;
  description?: T;
  utmSource?: T;
  utmMedium?: T;
  utmCampaign?: T;
  utmTerm?: T;
  utmContent?: T;
  conversionType?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "logs_select".
 */
export interface LogsSelect<T extends boolean = true> {
  action?: T;
  description?: T;
  timestamp?: T;
  status?: T;
  data?: T;
  order?: T;
  payment?: T;
  ipAddress?: T;
  userAgent?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "redirects_select".
 */
export interface RedirectsSelect<T extends boolean = true> {
  from?: T;
  to?:
    | T
    | {
        type?: T;
        reference?: T;
        url?: T;
      };
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "forms_select".
 */
export interface FormsSelect<T extends boolean = true> {
  type?: T;
  title?: T;
  fields?:
    | T
    | {
        checkbox?:
          | T
          | {
              name?: T;
              label?: T;
              width?: T;
              required?: T;
              defaultValue?: T;
              id?: T;
              blockName?: T;
            };
        country?:
          | T
          | {
              name?: T;
              label?: T;
              width?: T;
              required?: T;
              id?: T;
              blockName?: T;
            };
        email?:
          | T
          | {
              name?: T;
              label?: T;
              width?: T;
              required?: T;
              id?: T;
              blockName?: T;
            };
        message?:
          | T
          | {
              message?: T;
              id?: T;
              blockName?: T;
            };
        number?:
          | T
          | {
              name?: T;
              label?: T;
              width?: T;
              defaultValue?: T;
              required?: T;
              id?: T;
              blockName?: T;
            };
        select?:
          | T
          | {
              name?: T;
              label?: T;
              width?: T;
              defaultValue?: T;
              placeholder?: T;
              options?:
                | T
                | {
                    label?: T;
                    value?: T;
                    id?: T;
                  };
              required?: T;
              id?: T;
              blockName?: T;
            };
        state?:
          | T
          | {
              name?: T;
              label?: T;
              width?: T;
              required?: T;
              id?: T;
              blockName?: T;
            };
        text?:
          | T
          | {
              name?: T;
              label?: T;
              width?: T;
              defaultValue?: T;
              required?: T;
              id?: T;
              blockName?: T;
            };
        textarea?:
          | T
          | {
              name?: T;
              label?: T;
              width?: T;
              defaultValue?: T;
              required?: T;
              id?: T;
              blockName?: T;
            };
      };
  submitButtonLabel?: T;
  confirmationType?: T;
  confirmationMessage?: T;
  redirect?:
    | T
    | {
        url?: T;
      };
  emails?:
    | T
    | {
        emailTo?: T;
        cc?: T;
        bcc?: T;
        replyTo?: T;
        emailFrom?: T;
        subject?: T;
        message?: T;
        id?: T;
      };
  status?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "form-submissions_select".
 */
export interface FormSubmissionsSelect<T extends boolean = true> {
  form?: T;
  submissionData?:
    | T
    | {
        field?: T;
        value?: T;
        id?: T;
      };
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "search_select".
 */
export interface SearchSelect<T extends boolean = true> {
  title?: T;
  priority?: T;
  doc?: T;
  slug?: T;
  meta?:
    | T
    | {
        title?: T;
        description?: T;
        image?: T;
      };
  categories?:
    | T
    | {
        relationTo?: T;
        id?: T;
        title?: T;
      };
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "exports_select".
 */
export interface ExportsSelect<T extends boolean = true> {
  name?: T;
  format?: T;
  limit?: T;
  sort?: T;
  locale?: T;
  drafts?: T;
  selectionToUse?: T;
  fields?: T;
  collectionSlug?: T;
  where?: T;
  updatedAt?: T;
  createdAt?: T;
  url?: T;
  thumbnailURL?: T;
  filename?: T;
  mimeType?: T;
  filesize?: T;
  width?: T;
  height?: T;
  focalX?: T;
  focalY?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "payload-jobs_select".
 */
export interface PayloadJobsSelect<T extends boolean = true> {
  input?: T;
  taskStatus?: T;
  completedAt?: T;
  totalTried?: T;
  hasError?: T;
  error?: T;
  log?:
    | T
    | {
        executedAt?: T;
        completedAt?: T;
        taskSlug?: T;
        taskID?: T;
        input?: T;
        output?: T;
        state?: T;
        error?: T;
        id?: T;
      };
  taskSlug?: T;
  queue?: T;
  waitUntil?: T;
  processing?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "payload-locked-documents_select".
 */
export interface PayloadLockedDocumentsSelect<T extends boolean = true> {
  document?: T;
  globalSlug?: T;
  user?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "payload-preferences_select".
 */
export interface PayloadPreferencesSelect<T extends boolean = true> {
  user?: T;
  key?: T;
  value?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "payload-migrations_select".
 */
export interface PayloadMigrationsSelect<T extends boolean = true> {
  name?: T;
  batch?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "header".
 */
export interface Header {
  id: number;
  logo?: (number | null) | Media;
  title?: string | null;
  seo?: {
    title?: string | null;
    description?: string | null;
    /**
     * Image used for social media link previews (recommended 1200x630 px).
     */
    image?: (number | null) | Media;
  };
  navItems?:
    | {
        link: {
          type?: ('reference' | 'custom') | null;
          newTab?: boolean | null;
          reference?:
            | ({
                relationTo: 'pages';
                value: number | Page;
              } | null)
            | ({
                relationTo: 'posts';
                value: number | Post;
              } | null);
          url?: string | null;
          label: string;
        };
        children?:
          | {
              link: {
                type?: ('reference' | 'custom') | null;
                newTab?: boolean | null;
                reference?:
                  | ({
                      relationTo: 'pages';
                      value: number | Page;
                    } | null)
                  | ({
                      relationTo: 'posts';
                      value: number | Post;
                    } | null);
                url?: string | null;
                label: string;
              };
              id?: string | null;
            }[]
          | null;
        id?: string | null;
      }[]
    | null;
  updatedAt?: string | null;
  createdAt?: string | null;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "footer".
 */
export interface Footer {
  id: number;
  logo?: (number | null) | Media;
  title?: string | null;
  description?: string | null;
  address?: string | null;
  email?: string | null;
  phoneNumber?: string | null;
  socials?:
    | {
        name?: string | null;
        link?: string | null;
        id?: string | null;
      }[]
    | null;
  contactTitle?: string | null;
  connectUsTitle?: string | null;
  aboutUsTitle?: string | null;
  navItems?:
    | {
        link: {
          type?: ('reference' | 'custom') | null;
          newTab?: boolean | null;
          reference?:
            | ({
                relationTo: 'pages';
                value: number | Page;
              } | null)
            | ({
                relationTo: 'posts';
                value: number | Post;
              } | null);
          url?: string | null;
          label: string;
        };
        id?: string | null;
      }[]
    | null;
  updatedAt?: string | null;
  createdAt?: string | null;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "header_select".
 */
export interface HeaderSelect<T extends boolean = true> {
  logo?: T;
  title?: T;
  seo?:
    | T
    | {
        title?: T;
        description?: T;
        image?: T;
      };
  navItems?:
    | T
    | {
        link?:
          | T
          | {
              type?: T;
              newTab?: T;
              reference?: T;
              url?: T;
              label?: T;
            };
        children?:
          | T
          | {
              link?:
                | T
                | {
                    type?: T;
                    newTab?: T;
                    reference?: T;
                    url?: T;
                    label?: T;
                  };
              id?: T;
            };
        id?: T;
      };
  updatedAt?: T;
  createdAt?: T;
  globalType?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "footer_select".
 */
export interface FooterSelect<T extends boolean = true> {
  logo?: T;
  title?: T;
  description?: T;
  address?: T;
  email?: T;
  phoneNumber?: T;
  socials?:
    | T
    | {
        name?: T;
        link?: T;
        id?: T;
      };
  contactTitle?: T;
  connectUsTitle?: T;
  aboutUsTitle?: T;
  navItems?:
    | T
    | {
        link?:
          | T
          | {
              type?: T;
              newTab?: T;
              reference?: T;
              url?: T;
              label?: T;
            };
        id?: T;
      };
  updatedAt?: T;
  createdAt?: T;
  globalType?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "TaskCreateCollectionExport".
 */
export interface TaskCreateCollectionExport {
  input: {
    name?: string | null;
    format: 'csv' | 'json';
    limit?: number | null;
    sort?: string | null;
    locale?: ('all' | 'en' | 'vi') | null;
    drafts?: ('yes' | 'no') | null;
    selectionToUse?: ('currentSelection' | 'currentFilters' | 'all') | null;
    fields?: string[] | null;
    collectionSlug: string;
    where?:
      | {
          [k: string]: unknown;
        }
      | unknown[]
      | string
      | number
      | boolean
      | null;
    user?: string | null;
    userCollection?: string | null;
    exportsCollection?: string | null;
  };
  output?: unknown;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "TaskSchedulePublish".
 */
export interface TaskSchedulePublish {
  input: {
    type?: ('publish' | 'unpublish') | null;
    locale?: string | null;
    doc?:
      | ({
          relationTo: 'pages';
          value: number | Page;
        } | null)
      | ({
          relationTo: 'posts';
          value: number | Post;
        } | null);
    global?: string | null;
    user?: (number | null) | Admin;
  };
  output?: unknown;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "BannerBlock".
 */
export interface BannerBlock {
  style: 'info' | 'warning' | 'error' | 'success';
  content: {
    root: {
      type: string;
      children: {
        type: string;
        version: number;
        [k: string]: unknown;
      }[];
      direction: ('ltr' | 'rtl') | null;
      format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | '';
      indent: number;
      version: number;
    };
    [k: string]: unknown;
  };
  id?: string | null;
  blockName?: string | null;
  blockType: 'banner';
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "CodeBlock".
 */
export interface CodeBlock {
  language?: ('typescript' | 'javascript' | 'css') | null;
  code: string;
  id?: string | null;
  blockName?: string | null;
  blockType: 'code';
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "auth".
 */
export interface Auth {
  [k: string]: unknown;
}


declare module 'payload' {
  export interface GeneratedTypes extends Config {}
}