import { PayloadRequest } from 'payload'
import { NextResponse } from 'next/server'
import { TICKET_STATUS } from '../constants'
import { USER_ROLE } from '@/collections/Users/<USER>'

interface CreateGiftTicketRequest {
  ownerId: number
  ticketIds: number[]
  recipientFirstName: string
  recipientLastName: string
  recipientEmail: string
  recipientPhone?: string
  message?: string
}

interface CreateOrFindUserResult {
  userId: number
  isNewUser: boolean
  error?: string
}

/**
 * Create or find user account for gift recipient
 */
async function createOrFindRecipientUser(
  email: string,
  firstName: string,
  lastName: string,
  phone: string | undefined,
  payload: any,
): Promise<CreateOrFindUserResult> {
  try {
    // First, try to find existing user
    const existingUsers = await payload.find({
      collection: 'users',
      where: { email: { equals: String(email).toLowerCase().trim() } },
      limit: 1,
    })

    if (existingUsers.docs.length > 0) {
      return {
        userId: existingUsers.docs[0].id,
        isNewUser: false,
      }
    }

    // Create new user account
    const newUser = await payload.create({
      collection: 'users',
      data: {
        email,
        firstName,
        lastName,
        phoneNumber: phone || '',
        ...(phone
          ? { phoneNumbers: [{ phone, createdAt: new Date().toISOString(), isUsing: true }] }
          : {}),
        role: USER_ROLE.user.value,
      },
    })

    return {
      userId: newUser.id,
      isNewUser: true,
    }
  } catch (error: any) {
    console.error('Error creating/finding recipient user:', error)
    return {
      userId: 0,
      isNewUser: false,
      error: `Failed to create/find user: ${error.message}`,
    }
  }
}

/**
 * Validate if tickets can be gifted
 */
async function validateTicketsForGifting(
  ticketIds: number[],
  ownerId: number,
  payload: any,
): Promise<{ valid: boolean; errors: string[]; tickets: any[] }> {
  const errors: string[] = []

  try {
    // Check if tickets exist and belong to owner
    const tickets = await payload.find({
      collection: 'tickets',
      where: {
        id: { in: ticketIds },
        user: { equals: ownerId },
        status: { equals: TICKET_STATUS.booked.value },
      },
      depth: 1,
    })

    if (tickets.docs.length !== ticketIds.length) {
      errors.push('Some tickets are not found, not owned by user, or not in booked status')
    }

    console.log('tickets', tickets.docs)

    // Check if any tickets are from past events
    const now = new Date()
    for (const ticket of tickets.docs) {
        
        console.log('ticket?.event?.startDatetime', ticket?.event?.startDatetime)

      if (ticket?.event?.startDatetime && new Date(ticket?.event?.startDatetime) < now) {
        errors.push(`Ticket ${ticket.ticketCode} is for a past event and cannot be gifted`)
      }
    }

    // Check if any tickets are already checked in
    const checkedInTickets = await payload.find({
      collection: 'checkinRecords',
      where: {
        ticket: { in: ticketIds },
        deletedAt: { equals: null },
      },
      limit: ticketIds.length,
    })

    if (checkedInTickets.docs.length > 0) {
      errors.push('Some tickets have already been checked in and cannot be gifted')
    }

    // Check if any tickets are already gifted
    const giftedTickets = tickets.docs.filter((ticket: any) => ticket.giftInfo?.isGifted)
    if (giftedTickets.length > 0) {
      errors.push('Some tickets have already been gifted')
    }

    return {
      valid: errors.length === 0,
      errors,
      tickets: tickets.docs,
    }
  } catch (error) {
    console.error('Error validating tickets for gifting:', error)
    return {
      valid: false,
      errors: [`Validation failed: ${(error as Error).message}`],
      tickets: [],
    }
  }
}

/**
 * Transfer tickets to recipient and mark as gifted
 */
async function transferTicketsToRecipient(
  ticketIds: number[],
  recipientUserId: number,
  recipientName: string,
  payload: any,
): Promise<{ success: boolean; errors: string[] }> {
  try {
    // Update each ticket
    for (const ticketId of ticketIds) {
      await payload.update({
        collection: 'tickets',
        id: ticketId,
        data: {
          giftInfo: {
            isGifted: true,
            attendeeName: recipientName,
            giftRecipient: recipientUserId,
            giftDate: new Date().toISOString(),
          },
        },
      })
    }

    return { success: true, errors: [] }
  } catch (error) {
    console.error('Error transferring tickets:', error)
    return {
      success: false,
      errors: [`Transfer failed: ${(error as Error).message}`],
    }
  }
}

export const createGiftTicket = async (req: PayloadRequest): Promise<Response> => {
  try {
    const body = ((await req.json?.()) || {}) as CreateGiftTicketRequest

    const {
      ownerId,
      ticketIds,
      recipientFirstName,
      recipientLastName,
      recipientEmail,
      recipientPhone,
    } = body

    console.log('body', body)

    // Validate required fields
    if (
      !ownerId ||
      !ticketIds?.length ||
      !recipientFirstName ||
      !recipientLastName ||
      !recipientEmail
    ) {
      return NextResponse.json({ message: 'Missing required fields' }, { status: 400 })
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    if (!emailRegex.test(recipientEmail)) {
      return NextResponse.json({ message: 'Invalid email format' }, { status: 400 })
    }

    const payload = req.payload

    // Verify owner exists
    try {
      await payload.findByID({
        collection: 'users',
        id: ownerId,
        depth: 0,
      })
    } catch (_error) {
      return NextResponse.json({ message: 'Ticket owner not found' }, { status: 404 })
    }

    // Validate tickets for gifting
    const validation = await validateTicketsForGifting(ticketIds, ownerId, payload)
    console.log('validation', validation)
    if (!validation.valid) {
      throw new Error(validation.errors.join('; '))
    }

    // Create or find recipient user
    const userResult = await createOrFindRecipientUser(
      recipientEmail,
      recipientFirstName,
      recipientLastName,
      recipientPhone,
      payload,
    )

    if (userResult.error) {
      throw new Error(userResult.error)
    }

    // Transfer tickets to recipient
    const transferResult = await transferTicketsToRecipient(
      ticketIds,
      userResult.userId,
      `${recipientFirstName || ''} ${recipientLastName || ''}`.trim(),
      payload,
    )

    if (!transferResult.success) {
      throw new Error(transferResult.errors.join('; '))
    }

    return NextResponse.json({
      success: true,
      message: 'Tickets gifted successfully',
      data: {
        recipientUserId: userResult.userId,
        isNewUser: userResult.isNewUser,
        transferredTickets: ticketIds.length,
      },
    })
  } catch (error) {
    console.error('Error creating gift ticket:', error)
    return NextResponse.json(
      { message: 'Failed to create gift ticket', error: (error as Error).message },
      { status: 500 },
    )
  }
}
