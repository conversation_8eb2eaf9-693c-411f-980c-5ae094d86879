import type { CollectionConfig } from 'payload'

export const GIFT_STATUS = {
  pending: {
    label: 'Pending',
    value: 'pending',
  },
  accepted: {
    label: 'Accepted',
    value: 'accepted',
  },
  rejected: {
    label: 'Rejected',
    value: 'rejected',
  },
  expired: {
    label: 'Expired',
    value: 'expired',
  },
} as const

export const GIFT_STATUSES = Object.values(GIFT_STATUS)

export const TicketGifts: CollectionConfig = {
  slug: 'ticket-gifts',
  admin: {
    useAsTitle: 'id',
    description: 'Manage ticket gift transfers between users',
  },
  fields: [
    {
      name: 'giftCode',
      type: 'text',
      required: true,
      unique: true,
      index: true,
      admin: {
        readOnly: true,
        description: 'Unique code for this gift transfer',
      },
    },
    {
      name: 'originalOwner',
      type: 'relationship',
      relationTo: 'users',
      required: true,
      index: true,
      admin: {
        readOnly: true,
        description: 'User who is gifting the tickets',
      },
    },
    {
      name: 'recipientEmail',
      type: 'email',
      required: true,
      index: true,
      admin: {
        readOnly: true,
        description: 'Email address of the gift recipient',
      },
    },
    {
      name: 'recipientFirstName',
      type: 'text',
      required: true,
      admin: {
        readOnly: true,
      },
    },
    {
      name: 'recipientLastName',
      type: 'text',
      required: true,
      admin: {
        readOnly: true,
      },
    },
    {
      name: 'recipientPhone',
      type: 'text',
      required: false,
      admin: {
        readOnly: true,
      },
    },
    {
      name: 'recipientUser',
      type: 'relationship',
      relationTo: 'users',
      required: false,
      index: true,
      admin: {
        description: 'User account created/linked when gift is accepted',
      },
    },
    {
      name: 'tickets',
      type: 'relationship',
      relationTo: 'tickets',
      hasMany: true,
      required: true,
      index: true,
      admin: {
        readOnly: true,
        description: 'Tickets being gifted',
      },
    },
    {
      name: 'status',
      type: 'select',
      options: GIFT_STATUSES,
      defaultValue: 'pending',
      required: true,
      index: true,
    },
    {
      name: 'message',
      type: 'textarea',
      required: false,
      admin: {
        readOnly: true,
        description: 'Optional message from the gift sender',
      },
    },
    {
      name: 'acceptedAt',
      type: 'date',
      required: false,
      admin: {
        date: {
          pickerAppearance: 'dayAndTime',
        },
      },
    },
    {
      name: 'rejectedAt',
      type: 'date',
      required: false,
      admin: {
        date: {
          pickerAppearance: 'dayAndTime',
        },
      },
    },
    {
      name: 'expiresAt',
      type: 'date',
      required: true,
      admin: {
        date: {
          pickerAppearance: 'dayAndTime',
        },
        description: 'Gift expires after this date (default: 7 days)',
      },
    },
    {
      name: 'processedBy',
      type: 'relationship',
      relationTo: 'admins',
      required: false,
      admin: {
        description: 'Admin who processed this gift (if manually processed)',
      },
    },
  ],
  hooks: {
    beforeChange: [
      async ({ data, operation }) => {
        if (operation === 'create') {
          // Generate unique gift code
          const timestamp = Date.now().toString(36)
          const random = Math.random().toString(36).substring(2, 8)
          data.giftCode = `GIFT-${timestamp}-${random}`.toUpperCase()
          
          // Set expiration date (7 days from now)
          if (!data.expiresAt) {
            const expirationDate = new Date()
            expirationDate.setDate(expirationDate.getDate() + 7)
            data.expiresAt = expirationDate.toISOString()
          }
        }
        
        // Set timestamps based on status changes
        if (data.status === 'accepted' && !data.acceptedAt) {
          data.acceptedAt = new Date().toISOString()
        }
        
        if (data.status === 'rejected' && !data.rejectedAt) {
          data.rejectedAt = new Date().toISOString()
        }
        
        return data
      },
    ],
  },
  access: {
    create: ({ req }) => {
      // Only admins can create gift records
      return req.user?.role === 'admin' || req.user?.role === 'super-admin'
    },
    read: ({ req }) => {
      // Admins can read all, users can only read their own gifts
      if (req.user?.role === 'admin' || req.user?.role === 'super-admin') {
        return true
      }
      
      return {
        or: [
          { originalOwner: { equals: req.user?.id } },
          { recipientUser: { equals: req.user?.id } },
        ],
      }
    },
    update: ({ req }) => {
      // Only admins can update gift records
      return req.user?.role === 'admin' || req.user?.role === 'super-admin'
    },
    delete: ({ req }) => {
      // Only super-admins can delete gift records
      return req.user?.role === 'super-admin'
    },
  },
}
